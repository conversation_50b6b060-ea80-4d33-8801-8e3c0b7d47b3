'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, Plus, Edit, Trash2, Save, X, AlertCircle, CheckCircle } from 'lucide-react';
import { getApiUrl } from '../utils/api';

interface IndicatorRequirement {
  indicator: string;
  requiredStatus: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

interface FuturesNotificationRule {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  minConfidence?: number;
  minStrength?: number;
  requiredTimeframes?: number;
  specificTimeframes?: string[];
  requiredSignalType?: 'BUY' | 'SELL' | 'HOLD';
  indicatorRequirements?: IndicatorRequirement[];
  advancedConditions?: any;
  enableSound: boolean;
  enableVisual: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  marketType: 'FUTURES';
  createdAt: string;
  updatedAt: string;
}

interface Message {
  type: 'success' | 'error';
  text: string;
}

const timeframeOptions = [
  { value: '5m', label: '5 minutes' },
  { value: '15m', label: '15 minutes' },
  { value: '1h', label: '1 hour' },
  { value: '4h', label: '4 hours' },
  { value: '1d', label: '1 day' }
];

const signalTypeOptions = [
  { value: 'BUY', label: 'Buy Signal' },
  { value: 'SELL', label: 'Sell Signal' },
  { value: 'HOLD', label: 'Hold Signal' }
];

const priorityOptions = [
  { value: 'LOW', label: 'Low' },
  { value: 'MEDIUM', label: 'Medium' },
  { value: 'HIGH', label: 'High' }
];

const indicatorOptions = [
  { value: 'RSI', label: 'RSI (Relative Strength Index)' },
  { value: 'MACD', label: 'MACD (Moving Average Convergence Divergence)' },
  { value: 'EMA_TREND', label: 'EMA Trend (Exponential Moving Average)' },
  { value: 'BOLLINGER_BANDS', label: 'Bollinger Bands' },
  { value: 'ADX', label: 'ADX (Average Directional Index)' }
];

const statusOptions = [
  { value: 'BULLISH', label: 'Bullish' },
  { value: 'BEARISH', label: 'Bearish' },
  { value: 'NEUTRAL', label: 'Neutral' }
];

const AVAILABLE_TIMEFRAMES = [
  { value: '5m', label: '5 Minutes' },
  { value: '15m', label: '15 Minutes' },
  { value: '1h', label: '1 Hour' },
  { value: '4h', label: '4 Hours' },
  { value: '1d', label: '1 Day' }
];

const AVAILABLE_INDICATORS = indicatorOptions;
const INDICATOR_STATUS_OPTIONS = statusOptions;

export default function FuturesNotificationRules() {
  const [rules, setRules] = useState<FuturesNotificationRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingRule, setEditingRule] = useState<FuturesNotificationRule | null>(null);
  const [message, setMessage] = useState<Message | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true,
    minConfidence: '',
    minStrength: '',
    requiredTimeframes: '',
    specificTimeframes: [] as string[],
    requiredSignalType: '',
    indicatorRequirements: [] as IndicatorRequirement[],
    enableSound: true,
    enableVisual: true,
    priority: 'MEDIUM' as 'LOW' | 'MEDIUM' | 'HIGH'
  });

  useEffect(() => {
    fetchRules();
  }, []);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl('/api/admin/futures-notification-rules'));
      if (response.ok) {
        const data = await response.json();
        setRules(data.data);
      } else {
        throw new Error('Failed to fetch futures notification rules');
      }
    } catch (error) {
      console.error('Error fetching futures notification rules:', error);
      setMessage({ type: 'error', text: 'Failed to load futures notification rules' });
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for timeframes
  const addTimeframeSlot = () => {
    setFormData(prev => ({
      ...prev,
      specificTimeframes: [...prev.specificTimeframes, '']
    }));
  };

  const removeTimeframeSlot = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specificTimeframes: prev.specificTimeframes.filter((_, i) => i !== index)
    }));
  };

  const updateTimeframeSlot = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      specificTimeframes: prev.specificTimeframes.map((tf, i) => i === index ? value : tf)
    }));
  };

  const getAvailableTimeframes = (currentIndex: number) => {
    const usedTimeframes = formData.specificTimeframes.filter((tf, i) => i !== currentIndex && tf !== '');
    return AVAILABLE_TIMEFRAMES.filter(tf => !usedTimeframes.includes(tf.value));
  };

  // Helper functions for indicator requirements
  const addIndicatorRequirement = () => {
    setFormData(prev => ({
      ...prev,
      indicatorRequirements: [...prev.indicatorRequirements, { indicator: '', requiredStatus: 'BULLISH' }]
    }));
  };

  const removeIndicatorRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      indicatorRequirements: prev.indicatorRequirements.filter((_, i) => i !== index)
    }));
  };

  const updateIndicatorRequirement = (index: number, field: 'indicator' | 'requiredStatus', value: string) => {
    setFormData(prev => ({
      ...prev,
      indicatorRequirements: prev.indicatorRequirements.map((req, i) =>
        i === index ? { ...req, [field]: value } : req
      )
    }));
  };

  const getAvailableIndicators = (currentIndex: number) => {
    const usedIndicators = formData.indicatorRequirements.filter((req, i) => i !== currentIndex && req.indicator !== '').map(req => req.indicator);
    return AVAILABLE_INDICATORS.filter(ind => !usedIndicators.includes(ind.value));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setMessage({ type: 'error', text: 'Rule name is required' });
      return;
    }

    try {
      setSaving(true);
      
      const payload = {
        ...formData,
        marketType: 'FUTURES',
        minConfidence: formData.minConfidence ? parseFloat(formData.minConfidence) : null,
        minStrength: formData.minStrength ? parseFloat(formData.minStrength) : null,
        requiredTimeframes: formData.requiredTimeframes ? parseInt(formData.requiredTimeframes) : null,
        requiredSignalType: formData.requiredSignalType || null,
        specificTimeframes: formData.specificTimeframes.length > 0 ? formData.specificTimeframes : null,
        indicatorRequirements: formData.indicatorRequirements.length > 0 ? formData.indicatorRequirements : null
      };

      const url = editingRule 
        ? getApiUrl(`/api/admin/futures-notification-rules/${editingRule.id}`)
        : getApiUrl('/api/admin/futures-notification-rules');
      
      const method = editingRule ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage({ 
          type: 'success', 
          text: `Futures notification rule ${editingRule ? 'updated' : 'created'} successfully` 
        });
        await fetchRules();
        resetForm();
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to save futures notification rule' });
      }
    } catch (error) {
      console.error('Error saving futures notification rule:', error);
      setMessage({ type: 'error', text: 'Failed to save futures notification rule' });
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (rule: FuturesNotificationRule) => {
    setEditingRule(rule);
    setFormData({
      name: rule.name,
      description: rule.description || '',
      isActive: rule.isActive,
      minConfidence: rule.minConfidence?.toString() || '',
      minStrength: rule.minStrength?.toString() || '',
      requiredTimeframes: rule.requiredTimeframes?.toString() || '',
      specificTimeframes: rule.specificTimeframes || [],
      requiredSignalType: rule.requiredSignalType || '',
      indicatorRequirements: rule.indicatorRequirements || [],
      enableSound: rule.enableSound,
      enableVisual: rule.enableVisual,
      priority: rule.priority
    });
    setShowForm(true);
    setMessage(null);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this futures notification rule?')) {
      return;
    }

    try {
      const response = await fetch(getApiUrl(`/api/admin/futures-notification-rules/${id}`), {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Futures notification rule deleted successfully' });
        await fetchRules();
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to delete futures notification rule' });
      }
    } catch (error) {
      console.error('Error deleting futures notification rule:', error);
      setMessage({ type: 'error', text: 'Failed to delete futures notification rule' });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      isActive: true,
      minConfidence: '',
      minStrength: '',
      requiredTimeframes: '',
      specificTimeframes: [],
      requiredSignalType: '',
      indicatorRequirements: [],
      enableSound: true,
      enableVisual: true,
      priority: 'MEDIUM'
    });
    setEditingRule(null);
    setShowForm(false);
  };



  const toggleTimeframe = (timeframe: string) => {
    setFormData(prev => ({
      ...prev,
      specificTimeframes: prev.specificTimeframes.includes(timeframe)
        ? prev.specificTimeframes.filter(tf => tf !== timeframe)
        : [...prev.specificTimeframes, timeframe]
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <TrendingUp className="h-7 w-7 text-orange-600" />
          <h1 className="text-3xl font-bold text-gray-900">Futures Notification Rules</h1>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors shadow-md font-medium"
        >
          <Plus className="h-5 w-5" />
          <span>Add Futures Rule</span>
        </button>
      </div>

      {message && (
        <div className={`p-4 rounded-lg flex items-center space-x-3 ${
          message.type === 'success'
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-600" />
          )}
          <span className="font-medium">{message.text}</span>
        </div>
      )}

      {showForm && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <div className="w-2 h-6 bg-orange-600 rounded-full mr-3"></div>
            {editingRule ? 'Edit Futures Rule' : 'Create New Futures Rule'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Rule Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Priority
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as 'LOW' | 'MEDIUM' | 'HIGH' }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                >
                  <option value="LOW">Low</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="HIGH">High</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Min Confidence (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={formData.minConfidence}
                  onChange={(e) => setFormData(prev => ({ ...prev, minConfidence: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Min Strength (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={formData.minStrength}
                  onChange={(e) => setFormData(prev => ({ ...prev, minStrength: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Specific Timeframes
                </label>
                <div className="space-y-3">
                  {formData.specificTimeframes.map((timeframe, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <select
                        value={timeframe}
                        onChange={(e) => updateTimeframeSlot(index, e.target.value)}
                        className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                      >
                        <option value="">Select Timeframe</option>
                        {getAvailableTimeframes(index).map((tf) => (
                          <option key={tf.value} value={tf.value}>
                            {tf.label}
                          </option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => removeTimeframeSlot(index)}
                        className="px-3 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addTimeframeSlot}
                    disabled={formData.specificTimeframes.length >= AVAILABLE_TIMEFRAMES.length}
                    className="flex items-center space-x-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add Timeframe</span>
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-semibold text-gray-800">
                  Indicator Requirements
                </label>
                <span className="text-xs text-gray-500">Optional - Select indicators with specific status requirements</span>
              </div>
              <div className="space-y-3">
                {formData.indicatorRequirements.map((requirement, index) => (
                  <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex-1">
                      <select
                        value={requirement.indicator}
                        onChange={(e) => updateIndicatorRequirement(index, 'indicator', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                      >
                        <option value="">Select Indicator</option>
                        {getAvailableIndicators(index).map((ind) => (
                          <option key={ind.value} value={ind.value}>
                            {ind.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="w-32">
                      <select
                        value={requirement.requiredStatus}
                        onChange={(e) => updateIndicatorRequirement(index, 'requiredStatus', e.target.value as 'BULLISH' | 'BEARISH' | 'NEUTRAL')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
                      >
                        {INDICATOR_STATUS_OPTIONS.map((status) => (
                          <option key={status.value} value={status.value}>
                            {status.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeIndicatorRequirement(index)}
                      className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addIndicatorRequirement}
                  disabled={formData.indicatorRequirements.length >= AVAILABLE_INDICATORS.length}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Indicator Requirement</span>
                </button>
                {formData.indicatorRequirements.length > 0 && (
                  <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-xs text-blue-700">
                      <strong>Note:</strong> All selected indicators must match their required status for the notification to trigger.
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Required Signal Type
              </label>
              <select
                value={formData.requiredSignalType}
                onChange={(e) => setFormData(prev => ({ ...prev, requiredSignalType: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900 font-medium"
              >
                <option value="">Any Signal Type</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
                <option value="HOLD">HOLD</option>
              </select>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-sm font-semibold text-gray-800 mb-4">Rule Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <label className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="w-5 h-5 text-orange-600 bg-white border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Active</span>
                </label>
                <label className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200">
                  <input
                    type="checkbox"
                    checked={formData.enableSound}
                    onChange={(e) => setFormData(prev => ({ ...prev, enableSound: e.target.checked }))}
                    className="w-5 h-5 text-orange-600 bg-white border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Sound Notifications</span>
                </label>
                <label className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200">
                  <input
                    type="checkbox"
                    checked={formData.enableVisual}
                    onChange={(e) => setFormData(prev => ({ ...prev, enableVisual: e.target.checked }))}
                    className="w-5 h-5 text-orange-600 bg-white border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Visual Notifications</span>
                </label>
              </div>
            </div>

            <div className="flex items-center space-x-4 pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={saving}
                className="flex items-center space-x-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 shadow-md font-medium"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <Save className="h-5 w-5" />
                )}
                <span>{editingRule ? 'Update' : 'Create'} Futures Rule</span>
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-md font-medium"
              >
                <X className="h-5 w-5" />
                <span>Cancel</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Rules Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-8 py-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Active Futures Notification Rules</h2>
          <p className="text-gray-600 mt-1">
            Manage notification rules for futures trading signals. Rules are checked every 30 seconds against live futures data.
          </p>
        </div>

        {rules.length === 0 ? (
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Futures Notification Rules</h3>
            <p className="text-gray-600 mb-6">
              Create your first futures notification rule to get alerts when trading signals match your criteria.
            </p>
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center space-x-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              <Plus className="h-5 w-5" />
              <span>Create First Rule</span>
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-8 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rule Details
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conditions
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Settings
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {rules.map((rule) => (
                  <tr key={rule.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-8 py-6 whitespace-nowrap">
                      <div>
                        <div className="text-base font-semibold text-gray-900">{rule.name}</div>
                        {rule.description && (
                          <div className="text-sm text-gray-600 mt-1">{rule.description}</div>
                        )}
                        <div className="text-xs text-orange-600 mt-1 font-medium">FUTURES</div>
                      </div>
                    </td>
                    <td className="px-8 py-6 whitespace-nowrap text-sm text-gray-700">
                      <div className="space-y-2">
                        {rule.minConfidence && (
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span>Confidence ≥ {rule.minConfidence}%</span>
                          </div>
                        )}
                        {rule.minStrength && (
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>Strength ≥ {rule.minStrength}%</span>
                          </div>
                        )}
                        {rule.requiredSignalType && (
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                            <span>Signal: {rule.requiredSignalType}</span>
                          </div>
                        )}
                        {rule.specificTimeframes && rule.specificTimeframes.length > 0 && (
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                            <span>Timeframes: {rule.specificTimeframes.join(', ')}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-8 py-6 whitespace-nowrap text-sm text-gray-700">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${rule.priority === 'HIGH' ? 'bg-red-500' : rule.priority === 'MEDIUM' ? 'bg-yellow-500' : 'bg-gray-500'}`}></span>
                          <span>{rule.priority} Priority</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          {rule.enableSound && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Sound</span>
                          )}
                          {rule.enableVisual && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Visual</span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-8 py-6 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                        rule.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {rule.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-8 py-6 whitespace-nowrap text-sm font-medium space-x-3">
                      <button
                        onClick={() => handleEdit(rule)}
                        className="text-orange-600 hover:text-orange-900 transition-colors"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(rule.id)}
                        className="text-red-600 hover:text-red-900 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
