'use client';

import { useState, useEffect, useRef } from 'react';
import { Bell, X, AlertCircle, CheckCircle, Info, Refresh<PERSON><PERSON>, Eye } from 'lucide-react';
import { io, Socket } from 'socket.io-client';
import { getWebSocketUrl, getApiUrl } from '../utils/api';
import Link from 'next/link';
import { playNotificationSound } from '../utils/soundManager';

interface FuturesNotificationPayload {
  id: string;
  title: string;
  message: string;
  type: 'STRONG_SIGNAL' | 'ALERT' | 'WARNING' | 'EARLY_WARNING_ALERT';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  hasVisual: boolean;
  symbol: string;
  signal: string;
  confidence?: number;
  strength?: number;
  timeframe?: string;
  ruleId?: string;
  ruleName?: string;
  marketType?: string;
  createdAt: string;
}

interface FuturesNotification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  symbol: string;
  signal: string;
  confidence?: number;
  strength?: number;
  timeframe?: string;
  ruleId?: string;
  ruleName?: string;
  marketType?: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

export default function FuturesNotificationSystem() {
  const [notifications, setNotifications] = useState<FuturesNotificationPayload[]>([]);
  const [allNotifications, setAllNotifications] = useState<FuturesNotification[]>([]);
  const [showPanel, setShowPanel] = useState(false);
  const [showAllNotifications, setShowAllNotifications] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    // Initialize WebSocket connection for futures notifications
    const initializeSocket = () => {
      try {
        const socket = io(getWebSocketUrl(), {
          transports: ['websocket'],
          upgrade: false,
          timeout: 20000,
          forceNew: true
        });

        socket.on('connect', () => {
          console.log('🔔 Connected to futures notification WebSocket');
        });

        socket.on('disconnect', () => {
          console.log('🔔 Disconnected from futures notification WebSocket');
        });

        // Listen for futures notifications specifically
        socket.on('notification', (payload: FuturesNotificationPayload) => {
          console.log('🔔 FuturesNotificationSystem received notification:', payload.title);

          // Only handle futures notifications
          if (payload.marketType === 'FUTURES' || payload.title?.includes('FUTURES:')) {
            console.log('🔔 Processing futures notification:', payload.title);

            // Add to notifications list
            setNotifications(prev => [payload, ...prev]);

            // Update unread count
            setUnreadCount(prev => prev + 1);

            // Play notification sound if enabled
            if (payload.hasVisual) {
              playNotificationSound().catch(e =>
                console.log('Could not play futures notification sound:', e)
              );
            }

            // Auto-remove toast notification after 30 seconds
            setTimeout(() => {
              removeNotification(payload.id);
            }, 30000);
          }
        });

        socketRef.current = socket;
      } catch (error) {
        console.error('Error initializing futures notification socket:', error);
      }
    };

    initializeSocket();

    // Load initial unread count for futures notifications
    loadUnreadCount();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  const loadUnreadCount = async () => {
    try {
      const response = await fetch(getApiUrl('/api/notifications?unread=true&marketType=FUTURES'));
      if (response.ok) {
        const data = await response.json();
        setUnreadCount(data.total || 0);
      }
    } catch (error) {
      console.error('Error loading futures unread count:', error);
    }
  };

  const loadAllNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl('/api/notifications?marketType=FUTURES&limit=100'));
      if (response.ok) {
        const data = await response.json();
        setAllNotifications(data.data || []);
      }
    } catch (error) {
      console.error('Error loading all futures notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshNotifications = async () => {
    setRefreshing(true);
    await Promise.all([
      loadUnreadCount(),
      showAllNotifications ? loadAllNotifications() : Promise.resolve()
    ]);
    setRefreshing(false);
  };

  const clearAllRecentNotifications = () => {
    setNotifications([]);
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await fetch(getApiUrl(`/api/notifications/${notificationId}/read`), {
        method: 'PUT'
      });
      
      // Update local state
      setAllNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking futures notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await fetch(getApiUrl('/api/notifications/mark-all-read?marketType=FUTURES'), {
        method: 'PUT'
      });
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all futures notifications as read:', error);
    }
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'STRONG_SIGNAL':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'ALERT':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'WARNING':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'border-red-500 bg-red-50';
      case 'MEDIUM':
        return 'border-yellow-500 bg-yellow-50';
      case 'LOW':
        return 'border-green-500 bg-green-50';
      default:
        return 'border-blue-500 bg-blue-50';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <>
      {/* Futures Notification Bell */}
      <div className="relative">
        <button
          onClick={() => {
            const newShowPanel = !showPanel;
            setShowPanel(newShowPanel);
            // Auto-refresh notifications when opening the panel
            if (newShowPanel) {
              refreshNotifications();
            }
          }}
          className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 rounded-md"
          title="Futures Notifications"
        >
          <Bell className="h-6 w-6" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </button>

        {/* Futures Notification Panel */}
        {showPanel && (
          <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-900">Futures Notifications</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={refreshNotifications}
                    disabled={refreshing}
                    className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-50"
                    title="Refresh futures notifications"
                  >
                    <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                  </button>
                  {!showAllNotifications && notifications.length > 0 && (
                    <button
                      onClick={clearAllRecentNotifications}
                      className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded border border-gray-300"
                    >
                      Clear Recent
                    </button>
                  )}
                  <button
                    onClick={() => setShowPanel(false)}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Toggle between Recent and All */}
              <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => {
                    setShowAllNotifications(false);
                  }}
                  className={`flex-1 px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    !showAllNotifications
                      ? 'bg-white text-orange-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Recent
                </button>
                <button
                  onClick={() => {
                    setShowAllNotifications(true);
                    if (allNotifications.length === 0) {
                      loadAllNotifications();
                    }
                  }}
                  className={`flex-1 px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    showAllNotifications
                      ? 'bg-white text-orange-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  All
                </button>
              </div>

              {/* Mark all as read button */}
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="mt-2 text-xs text-orange-600 hover:text-orange-800 font-medium"
                >
                  Mark all as read ({unreadCount})
                </button>
              )}
            </div>

            <div className="max-h-96 overflow-y-auto">
              {loading ? (
                <div className="p-4 text-center text-gray-500">
                  <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                  Loading futures notifications...
                </div>
              ) : (
                (() => {
                  const displayNotifications = showAllNotifications ? allNotifications : notifications;
                  return displayNotifications.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      {showAllNotifications ? 'No futures notifications found' : 'No recent futures notifications'}
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-200">
                      {displayNotifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-4 border-l-4 ${getPriorityColor(notification.priority)} hover:bg-gray-50 transition-colors`}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 mt-1">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {notification.title}
                                  </p>
                                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                                    {notification.message}
                                  </p>
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>{formatTimeAgo(notification.createdAt)}</span>
                                    {notification.symbol && (
                                      <Link
                                        href={`/futures/analysis/${notification.symbol}`}
                                        className="text-orange-600 hover:text-orange-800 font-medium"
                                        onClick={() => setShowPanel(false)}
                                      >
                                        {notification.symbol}
                                      </Link>
                                    )}
                                    {notification.confidence && (
                                      <span>Confidence: {notification.confidence.toFixed(1)}%</span>
                                    )}
                                  </div>
                                </div>
                                {showAllNotifications && 'isRead' in notification && !notification.isRead && (
                                  <button
                                    onClick={() => markAsRead(notification.id)}
                                    className="ml-2 p-1 text-gray-400 hover:text-gray-600"
                                    title="Mark as read"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                })()
              )}
            </div>

            {(() => {
              const displayNotifications = showAllNotifications ? allNotifications : notifications;
              return displayNotifications.length > 0 && (
                <div className="p-3 border-t border-gray-200 bg-gradient-to-r from-orange-50 to-orange-100">
                  <div className="text-center">
                    <div className="text-xs font-medium text-gray-700">
                      {displayNotifications.length} {showAllNotifications ? 'total' : 'recent'} futures notification{displayNotifications.length !== 1 ? 's' : ''}
                    </div>
                    {unreadCount > 0 && (
                      <div className="text-xs text-orange-600 mt-1">
                        {unreadCount} unread
                      </div>
                    )}
                    {!showAllNotifications && notifications.length > 0 && (
                      <div className="text-xs text-gray-500 mt-1">
                        Notifications persist until page refresh
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}
          </div>
        )}
      </div>

      {/* Enhanced Futures Toast Notifications */}
      <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
        {notifications.slice(0, 5).map((notification, index) => (
          <div
            key={`futures-toast-${notification.id}`}
            className={`w-full bg-gradient-to-r ${
              notification.priority === 'HIGH'
                ? 'from-red-500 to-red-600'
                : notification.priority === 'MEDIUM'
                ? 'from-orange-500 to-orange-600'
                : 'from-green-500 to-green-600'
            } shadow-2xl rounded-xl border border-white/20 backdrop-blur-sm transform transition-all duration-500 ease-out animate-slide-in-right`}
            style={{
              animationDelay: `${index * 100}ms`,
              transform: `translateY(${index * 4}px) scale(${1 - index * 0.02})`
            }}
          >
            <div className="p-4 text-white">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    {notification.type === 'STRONG_SIGNAL' ? (
                      <CheckCircle className="h-5 w-5 text-white" />
                    ) : notification.type === 'ALERT' ? (
                      <AlertCircle className="h-5 w-5 text-white" />
                    ) : (
                      <Info className="h-5 w-5 text-white" />
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-bold text-white truncate">
                      🔮 {notification.symbol} {notification.signal}
                    </p>
                    <span className="text-xs text-white/80 bg-white/20 px-2 py-0.5 rounded-full">
                      {notification.priority}
                    </span>
                  </div>

                  <p className="text-xs text-white/90 mb-2 line-clamp-2">
                    {notification.message}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1">
                        <span className="text-xs font-medium text-white">
                          C: {(notification.confidence || 0).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1">
                        <span className="text-xs font-medium text-white">
                          S: {(notification.strength || 0).toFixed(0)}%
                        </span>
                      </div>
                      {notification.timeframe && (
                        <div className="flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1">
                          <span className="text-xs font-medium text-white">
                            {notification.timeframe}
                          </span>
                        </div>
                      )}
                    </div>

                    <button
                      onClick={() => removeNotification(notification.id)}
                      className="ml-2 p-1 hover:bg-white/20 rounded-full transition-colors duration-200"
                      title="Dismiss"
                    >
                      <X className="h-4 w-4 text-white/80 hover:text-white" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Progress bar for visual appeal */}
              <div className="mt-3 w-full bg-white/20 rounded-full h-1">
                <div
                  className="bg-white h-1 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.max(notification.confidence || 0, notification.strength || 0)}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
