'use client';

import Link from 'next/link';

interface FuturesTimeframeAnalysisSectionProps {
  timeframe: string;
  notification: any;
  symbol: string;
  exchange: string;
  currentPrice?: number;
}

export default function FuturesTimeframeAnalysisSection({ 
  timeframe, 
  notification, 
  symbol, 
  exchange, 
  currentPrice 
}: FuturesTimeframeAnalysisSectionProps) {
  
  // Get technical indicators for this timeframe
  const getTechnicalIndicators = () => {
    if (notification.timeframe === 'multi' && notification.technicalIndicators && notification.technicalIndicators[timeframe]) {
      return notification.technicalIndicators[timeframe].technicalIndicators || {};
    } else if (notification.technicalIndicators) {
      return notification.technicalIndicators;
    }
    return {};
  };

  // Get chart patterns for this timeframe
  const getChartPatterns = () => {
    if (notification.timeframe === 'multi' && notification.technicalIndicators && notification.technicalIndicators[timeframe]) {
      return notification.technicalIndicators[timeframe].chartPatterns || [];
    } else if (notification.chartPatterns) {
      return notification.chartPatterns;
    }
    return [];
  };

  // Get candlestick patterns for this timeframe
  const getCandlestickPatterns = () => {
    if (notification.timeframe === 'multi' && notification.technicalIndicators && notification.technicalIndicators[timeframe]) {
      return notification.technicalIndicators[timeframe].candlestickPatterns || [];
    } else if (notification.candlestickPatterns) {
      return notification.candlestickPatterns;
    }
    return [];
  };

  // Get signal data for this timeframe
  const getSignalData = () => {
    if (notification.timeframe === 'multi' && notification.multiTimeframeData) {
      const timeframeData = notification.multiTimeframeData.find((data: any) => data.timeframe === timeframe);
      return timeframeData?.signalData || {};
    } else if (notification.signalData) {
      return notification.signalData;
    }
    return {};
  };

  const technicalIndicators = getTechnicalIndicators();
  const chartPatterns = getChartPatterns();
  const candlestickPatterns = getCandlestickPatterns();
  const signalData = getSignalData();

  // Get signal-based colors for bars and backgrounds
  const getSignalBarColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return 'bg-green-500';
      case 'SELL':
        return 'bg-red-500';
      case 'HOLD':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getSignalTextColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return 'text-green-400';
      case 'SELL':
        return 'text-red-400';
      case 'HOLD':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getSignalBgColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return 'bg-green-900/40 border-green-600/50';
      case 'SELL':
        return 'bg-red-900/40 border-red-600/50';
      case 'HOLD':
        return 'bg-yellow-900/40 border-yellow-600/50';
      default:
        return 'bg-gray-900/40 border-gray-600/50';
    }
  };

  // Get indicator status color
  const getIndicatorStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'bullish':
        return 'text-green-400';
      case 'bearish':
        return 'text-red-400';
      case 'neutral':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  // Get pattern confidence color
  const getPatternConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400';
    if (confidence >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const signal = signalData.signal || notification.signal || 'HOLD';
  const confidence = signalData.confidence || notification.confidence || 0;
  const strength = signalData.strength || notification.strength || 0;

  return (
    <div className={`bg-gray-800 rounded-lg overflow-hidden border ${getSignalBgColor(signal).split(' ')[1]}`}>
      {/* Timeframe Header */}
      <div className={`p-4 border-b border-gray-700 ${getSignalBgColor(signal)}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold text-white">🔮 {timeframe} Futures Analysis</h3>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              signal === 'BUY' ? 'bg-green-100 text-green-800' :
              signal === 'SELL' ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {signal}
            </span>
          </div>
          <div className="flex items-center gap-4 text-sm text-gray-300">
            <span>Confidence: <span className={getSignalTextColor(signal)}>{confidence.toFixed(1)}%</span></span>
            <span>Strength: <span className={getSignalTextColor(signal)}>{strength.toFixed(1)}%</span></span>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Technical Indicators */}
          <div className="bg-gray-900/50 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-white mb-4">📊 Technical Indicators</h4>
            <div className="space-y-3">
              {technicalIndicators.rsi && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">RSI:</span>
                  <span className={`font-medium ${getIndicatorStatusColor(technicalIndicators.rsi.status)}`}>
                    {technicalIndicators.rsi.value?.toFixed(2)} ({technicalIndicators.rsi.status})
                  </span>
                </div>
              )}
              {technicalIndicators.macd && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">MACD:</span>
                  <span className={`font-medium ${getIndicatorStatusColor(technicalIndicators.macd.status)}`}>
                    {technicalIndicators.macd.value?.toFixed(4)} ({technicalIndicators.macd.status})
                  </span>
                </div>
              )}
              {technicalIndicators.ema_trend && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">EMA Trend:</span>
                  <span className={`font-medium ${getIndicatorStatusColor(technicalIndicators.ema_trend.status)}`}>
                    {technicalIndicators.ema_trend.status}
                  </span>
                </div>
              )}
              {technicalIndicators.bollinger_bands && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Bollinger Bands:</span>
                  <span className={`font-medium ${getIndicatorStatusColor(technicalIndicators.bollinger_bands.status)}`}>
                    {technicalIndicators.bollinger_bands.status}
                  </span>
                </div>
              )}
              {technicalIndicators.adx && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">ADX:</span>
                  <span className={`font-medium ${getIndicatorStatusColor(technicalIndicators.adx.status)}`}>
                    {technicalIndicators.adx.value?.toFixed(2)} ({technicalIndicators.adx.status})
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Chart Patterns & Candlestick Patterns */}
          <div className="bg-gray-900/50 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-white mb-4">📈 Patterns</h4>
            
            {/* Chart Patterns */}
            {chartPatterns.length > 0 && (
              <div className="mb-4">
                <h5 className="text-sm font-medium text-gray-400 mb-2">Chart Patterns:</h5>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {chartPatterns.slice(0, 5).map((pattern: any, index: number) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="text-gray-300">{pattern.name}</span>
                      <span className={`font-medium ${getPatternConfidenceColor(pattern.confidence)}`}>
                        {pattern.confidence?.toFixed(1)}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Candlestick Patterns */}
            {candlestickPatterns.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-400 mb-2">Candlestick Patterns:</h5>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {candlestickPatterns.slice(0, 5).map((pattern: any, index: number) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="text-gray-300">{pattern.name}</span>
                      <span className={`font-medium ${getPatternConfidenceColor(pattern.confidence)}`}>
                        {pattern.confidence?.toFixed(1)}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {chartPatterns.length === 0 && candlestickPatterns.length === 0 && (
              <p className="text-gray-500 text-sm">No significant patterns detected</p>
            )}
          </div>
        </div>

        {/* Trading Signal Summary */}
        <div className={`mt-6 p-4 rounded-lg border ${getSignalBgColor(signal)}`}>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold text-white mb-2">🎯 Trading Signal</h4>
              <p className="text-gray-300 text-sm">
                Based on {timeframe} futures analysis for {symbol}
              </p>
            </div>
            <div className="text-right">
              <div className={`text-2xl font-bold ${getSignalTextColor(signal)} mb-1`}>
                {signal}
              </div>
              <div className="text-sm text-gray-400">
                {confidence.toFixed(1)}% confidence
              </div>
            </div>
          </div>
          
          {/* Signal Strength Bars */}
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <div className="flex justify-between text-sm text-gray-400 mb-1">
                <span>Confidence</span>
                <span>{confidence.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getSignalBarColor(signal)}`}
                  style={{ width: `${Math.min(confidence, 100)}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm text-gray-400 mb-1">
                <span>Strength</span>
                <span>{strength.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getSignalBarColor(signal)}`}
                  style={{ width: `${Math.min(strength, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="mt-4 text-center">
            <Link
              href={`/futures/analysis/${symbol.toLowerCase()}?timeframe=${timeframe}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-lg transition-colors"
            >
              🔮 View Full Futures Analysis
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
