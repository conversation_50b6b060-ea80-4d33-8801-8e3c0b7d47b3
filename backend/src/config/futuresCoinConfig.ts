import { BinanceFuturesService } from '../Services/binanceFuturesService';
import { logDebug, logError, logInfo } from '../utils/logger';

// Top 50 USDT Perpetual Futures contracts by volume and market cap
// These are the most liquid and actively traded futures contracts
export const TOP_50_FUTURES_SYMBOLS = [
  // Top Tier - Highest Volume & Liquidity
  'BTCUSDT',    // Bitcoin
  'ETHUSDT',    // Ethereum
  'BNBUSDT',    // Binance Coin
  'SOLUSDT',    // Solana
  'XRPUSDT',    // Ripple
  'ADAUSDT',    // Cardano
  'AVAXUSDT',   // Avalanche
  'DOGEUSDT',   // Dog<PERSON>oin
  'DOTUSDT',    // <PERSON><PERSON>t
  'MATICUSDT',  // Polygon

  // High Volume Tier
  'LINKUSDT',   // Chainlink
  'LTCUSDT',    // Litecoin
  'UNIUSDT',    // Uniswap
  'ATOMUSDT',   // Cosmos
  'ETCUSDT',    // Ethereum Classic
  'XLMUSDT',    // Stellar
  'BCHUSDT',    // Bitcoin Cash
  'FILUSDT',    // Filecoin
  'TRXUSDT',    // TRON
  'NEARUSDT',   // NEAR Protocol

  // Medium-High Volume Tier
  'APTUSDT',    // Aptos
  'ARBUSDT',    // Arbitrum
  'OPUSDT',     // Optimism
  'INJUSDT',    // Injective
  'SUIUSDT',    // Sui
  'STXUSDT',    // Stacks
  'TIAUSDT',    // Celestia
  'SEIUSDT',    // Sei
  'WLDUSDT',    // Worldcoin
  'PEPEUSDT',   // Pepe

  // Active Trading Tier
  'AAVEUSDT',   // Aave
  'MKRUSDT',    // Maker
  'LDOUSDT',    // Lido DAO
  'RNDRUSDT',   // Render Token
  'FETUSDT',    // Fetch.ai
  'GRTUSDT',    // The Graph
  'SANDUSDT',   // The Sandbox
  'MANAUSDT',   // Decentraland
  'ICPUSDT',    // Internet Computer
  'FLOWUSDT',   // Flow

  // Emerging High Volume
  'THETAUSDT',  // Theta Network
  'AXSUSDT',    // Axie Infinity
  'ALGOUSDT',   // Algorand
  'EGLDUSDT',   // MultiversX
  'XTZUSDT',    // Tezos
  'EOSUSDT',    // EOS
  'KLAYUSDT',   // Klaytn
  'VETUSDT',    // VeChain
  'FTMUSDT',    // Fantom
  'HBARUSDT'    // Hedera
];

// Validate that we have exactly 50 symbols
if (TOP_50_FUTURES_SYMBOLS.length !== 50) {
  throw new Error(`Expected 50 futures symbols, but got ${TOP_50_FUTURES_SYMBOLS.length}`);
}

// Ensure all symbols end with USDT (futures perpetual contracts)
TOP_50_FUTURES_SYMBOLS.forEach(symbol => {
  if (!symbol.endsWith('USDT')) {
    throw new Error(`Invalid futures symbol: ${symbol}. All futures symbols must end with USDT`);
  }
});

/**
 * Fetch top 50 futures coins from Binance WebSocket data
 * This function mirrors the spot coin configuration but for futures
 */
export async function fetchTop50FuturesFromWebSocket(): Promise<string[]> {
  try {
    logInfo('🔄 Fetching top 50 futures coins configuration');

    // For now, return the curated list
    // In the future, this could dynamically fetch from Binance Futures API
    // and filter by volume, but the curated list ensures quality and stability
    
    logInfo(`✅ Loaded ${TOP_50_FUTURES_SYMBOLS.length} curated futures symbols`);
    logDebug('Futures symbols:', TOP_50_FUTURES_SYMBOLS.join(', '));

    return TOP_50_FUTURES_SYMBOLS;
  } catch (error) {
    logError('Error fetching top 50 futures coins', error as Error);
    
    // Fallback to a smaller set of most liquid futures
    const fallbackSymbols = [
      'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT',
      'ADAUSDT', 'AVAXUSDT', 'DOGEUSDT', 'DOTUSDT', 'MATICUSDT',
      'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'ETCUSDT',
      'XLMUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT', 'NEARUSDT'
    ];
    
    logInfo(`Using fallback futures symbols: ${fallbackSymbols.length} symbols`);
    return fallbackSymbols;
  }
}

/**
 * Get futures symbol information
 * This could be extended to include contract specifications, margin requirements, etc.
 */
export interface FuturesSymbolInfo {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  contractType: 'PERPETUAL';
  status: 'TRADING';
  tier: 'TOP' | 'HIGH' | 'MEDIUM' | 'ACTIVE' | 'EMERGING';
}

/**
 * Get detailed information about futures symbols
 */
export function getFuturesSymbolInfo(symbol: string): FuturesSymbolInfo | null {
  if (!TOP_50_FUTURES_SYMBOLS.includes(symbol)) {
    return null;
  }

  const baseAsset = symbol.replace('USDT', '');
  
  // Determine tier based on position in the list
  let tier: FuturesSymbolInfo['tier'];
  const index = TOP_50_FUTURES_SYMBOLS.indexOf(symbol);
  
  if (index < 10) tier = 'TOP';
  else if (index < 20) tier = 'HIGH';
  else if (index < 30) tier = 'MEDIUM';
  else if (index < 40) tier = 'ACTIVE';
  else tier = 'EMERGING';

  return {
    symbol,
    baseAsset,
    quoteAsset: 'USDT',
    contractType: 'PERPETUAL',
    status: 'TRADING',
    tier
  };
}

/**
 * Check if a symbol is in our futures trading list
 */
export function isFuturesSymbolSupported(symbol: string): boolean {
  return TOP_50_FUTURES_SYMBOLS.includes(symbol);
}

/**
 * Get futures symbols by tier
 */
export function getFuturesSymbolsByTier(tier: FuturesSymbolInfo['tier']): string[] {
  return TOP_50_FUTURES_SYMBOLS.filter(symbol => {
    const info = getFuturesSymbolInfo(symbol);
    return info?.tier === tier;
  });
}

/**
 * Get all futures symbols with their tiers
 */
export function getAllFuturesSymbolsWithTiers(): Array<{ symbol: string; tier: FuturesSymbolInfo['tier'] }> {
  return TOP_50_FUTURES_SYMBOLS.map(symbol => ({
    symbol,
    tier: getFuturesSymbolInfo(symbol)!.tier
  }));
}

// Export for external use
export { TOP_50_FUTURES_SYMBOLS as FUTURES_SYMBOLS };

// Log configuration on module load
logInfo(`Futures coin configuration loaded: ${TOP_50_FUTURES_SYMBOLS.length} symbols across 5 tiers`);
