import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import * as futuresCoinListController from '../controllers/futuresCoinListController';

const router = Router();

/**
 * @route GET /api/futures/coin-list/top50
 * @description Get top 50 futures coins with real-time confidence and strength signals
 * @returns Array of futures coins with technical analysis data
 */
router.get('/coin-list/top50', async<PERSON><PERSON><PERSON>(futuresCoinListController.getTop50FuturesCoins));

/**
 * @route GET /api/futures/coin-list/stats
 * @description Get futures coin list statistics and market summary
 * @returns Futures market statistics and performance metrics
 */
router.get('/coin-list/stats', asyncHandler(futuresCoinListController.getFuturesCoinListStats));

/**
 * @route GET /api/futures/analysis/:symbol
 * @description Get detailed technical analysis for a specific futures symbol
 * @param symbol - Futures symbol (e.g., BTCUSDT)
 * @query timeframe - Analysis timeframe (default: 1h)
 * @returns Detailed technical analysis including indicators, patterns, and signals
 */
router.get('/analysis/:symbol', asyncHandler(futuresCoinListController.getFuturesCoinAnalysis));

export default router;
