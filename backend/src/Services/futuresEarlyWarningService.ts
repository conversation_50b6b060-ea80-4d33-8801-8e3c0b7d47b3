import { Server as SocketIOServer } from 'socket.io';
import { BinanceFuturesService, BinanceFuturesTicker } from './binanceFuturesService';
import { FuturesTechnicalAnalysis } from './futuresTechnicalAnalysis';
import { logDebug, logError, logInfo } from '../utils/logger';

export interface FuturesEarlyWarningAlert {
  symbol: string;
  alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL';
  confidence: number;
  timeEstimateMin?: number;
  timeEstimateMax?: number;
  currentPrice: number;
  volume24h?: number;
  priceChange24h?: number;
  
  // Phase 1: Volume & Momentum Detection (25 points max)
  volumeSpike?: number;
  rsiMomentum?: number;
  emaConvergence?: number;
  phase1Score: number;
  
  // Phase 2: Order Flow Analysis (35 points max)
  bidAskImbalance?: number;
  priceAction?: number;
  phase2Score: number;
  
  // Phase 3: Whale Activity (40 points max)
  whaleActivity?: number;
  phase3Score: number;
  
  triggeredBy: string[];
}

export class FuturesEarlyWarningService {
  private futuresService: BinanceFuturesService;
  private technicalAnalysis: FuturesTechnicalAnalysis;
  private io: SocketIOServer;
  
  // Alert cooldown tracking (prevent spam)
  private lastAlertTimes: Map<string, number> = new Map();
  private readonly ALERT_COOLDOWN_MS = 5 * 60 * 1000; // 5 minutes
  
  // Volume tracking for spike detection
  private volumeHistory: Map<string, number[]> = new Map();
  private readonly VOLUME_HISTORY_SIZE = 20;
  
  // Wall tracking for whale activity detection
  private wallTracker: Map<string, any[]> = new Map();

  constructor(
    futuresService: BinanceFuturesService,
    technicalAnalysis: FuturesTechnicalAnalysis,
    io: SocketIOServer
  ) {
    this.futuresService = futuresService;
    this.technicalAnalysis = technicalAnalysis;
    this.io = io;
    
    logInfo('🚨 Futures Early Warning Service initialized');
  }

  /**
   * Analyze a futures symbol for early warning signals
   * Uses the same 3-phase analysis as spot but adapted for futures characteristics
   */
  async analyzeSymbol(symbol: string): Promise<FuturesEarlyWarningAlert | null> {
    try {
      logDebug(`Analyzing futures symbol for early warning: ${symbol}`);

      // Get current ticker data
      const ticker = this.futuresService.getCachedPrice(symbol);
      if (!ticker) {
        logDebug(`No ticker data available for futures ${symbol}`);
        return null;
      }

      const currentPrice = parseFloat(ticker.price);
      const volume24h = parseFloat(ticker.volume);
      const priceChange24h = parseFloat(ticker.priceChangePercent);

      // Phase 1: Volume & Momentum Detection (25 points max)
      const phase1Result = await this.analyzePhase1(symbol, currentPrice, volume24h, priceChange24h);
      
      // Phase 2: Order Flow Analysis (35 points max)
      const phase2Result = await this.analyzePhase2(symbol);
      
      // Phase 3: Whale Activity (40 points max)
      const phase3Result = await this.analyzePhase3(symbol);

      // Calculate overall confidence and alert type
      const alert = this.calculateOverallAlert(
        symbol, currentPrice, volume24h, priceChange24h,
        phase1Result, phase2Result, phase3Result
      );

      // Only process PUMP_LIKELY and DUMP_LIKELY signals, skip NEUTRAL
      if (alert && alert.alertType !== 'NEUTRAL' && alert.confidence > 0 && this.shouldCreateAlert(symbol, alert.alertType)) {
        logInfo(`Returning valid futures alert for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          triggeredBy: alert.triggeredBy
        });
        return alert;
      }

      return null;
    } catch (error) {
      logError(`Error analyzing futures symbol ${symbol} for early warning`, error as Error);
      return null;
    }
  }

  /**
   * Phase 1: Volume & Momentum Detection (25 points max)
   * Futures markets often show stronger volume spikes due to leverage
   */
  private async analyzePhase1(
    symbol: string,
    currentPrice: number,
    volume24h: number,
    priceChange24h: number
  ): Promise<{ score: number; details: any }> {
    let score = 0;
    const details: any = {};
    const triggeredBy: string[] = [];

    try {
      // Get technical indicators for momentum analysis
      const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, '15m');

      // 1. Volume Spike Analysis (10 points max)
      const volumeSpike = this.analyzeVolumeSpike(symbol, volume24h);
      if (volumeSpike.isSpike) {
        const volumeScore = Math.min(10, volumeSpike.multiplier * 2);
        score += volumeScore;
        details.volumeSpike = volumeScore;
        triggeredBy.push(`Volume spike ${volumeSpike.multiplier.toFixed(1)}x`);
      }

      // 2. RSI Momentum (8 points max)
      const rsiScore = this.analyzeRSIMomentum(indicators.rsi, priceChange24h);
      if (rsiScore > 0) {
        score += rsiScore;
        details.rsiMomentum = rsiScore;
        triggeredBy.push(`RSI momentum (${indicators.rsi.toFixed(1)})`);
      }

      // 3. EMA Convergence/Divergence (7 points max)
      const emaScore = this.analyzeEMAConvergence(indicators.ema20, indicators.ema50, currentPrice);
      if (emaScore > 0) {
        score += emaScore;
        details.emaConvergence = emaScore;
        triggeredBy.push('EMA convergence');
      }

      return { score, details: { ...details, triggeredBy } };
    } catch (error) {
      logError(`Error in futures Phase 1 analysis for ${symbol}`, error as Error);
      return { score: 0, details: {} };
    }
  }

  /**
   * Phase 2: Order Flow Analysis (35 points max)
   * Futures markets have deeper order books and more sophisticated order flow
   */
  private async analyzePhase2(symbol: string): Promise<{ score: number; details: any }> {
    let score = 0;
    const details: any = {};
    const triggeredBy: string[] = [];

    try {
      // For now, use simplified order flow analysis
      // In a full implementation, this would analyze:
      // - Bid/Ask imbalances
      // - Large order detection
      // - Order book depth changes
      // - Futures-specific metrics like funding rates

      // Placeholder implementation - would need order book data
      const mockBidAskImbalance = Math.random() * 20; // 0-20 points
      if (mockBidAskImbalance > 10) {
        score += mockBidAskImbalance;
        details.bidAskImbalance = mockBidAskImbalance;
        triggeredBy.push('Order flow imbalance');
      }

      // Price action analysis (15 points max)
      const priceActionScore = await this.analyzePriceAction(symbol);
      if (priceActionScore > 0) {
        score += priceActionScore;
        details.priceAction = priceActionScore;
        triggeredBy.push('Price action signal');
      }

      return { score, details: { ...details, triggeredBy } };
    } catch (error) {
      logError(`Error in futures Phase 2 analysis for ${symbol}`, error as Error);
      return { score: 0, details: {} };
    }
  }

  /**
   * Phase 3: Whale Activity (40 points max)
   * Futures markets show clearer whale activity due to leverage and position sizes
   */
  private async analyzePhase3(symbol: string): Promise<{ score: number; details: any }> {
    let score = 0;
    const details: any = {};
    const triggeredBy: string[] = [];

    try {
      // For now, use simplified whale activity analysis
      // In a full implementation, this would analyze:
      // - Large position changes
      // - Funding rate anomalies
      // - Open interest changes
      // - Liquidation cascades

      // Placeholder implementation
      const mockWhaleActivity = Math.random() * 30; // 0-30 points
      if (mockWhaleActivity > 15) {
        score += mockWhaleActivity;
        details.whaleActivity = mockWhaleActivity;
        triggeredBy.push('Whale activity detected');
      }

      return { score, details: { ...details, triggeredBy } };
    } catch (error) {
      logError(`Error in futures Phase 3 analysis for ${symbol}`, error as Error);
      return { score: 0, details: {} };
    }
  }

  /**
   * Analyze volume spike for futures
   * Futures often have more dramatic volume spikes due to leverage
   */
  private analyzeVolumeSpike(symbol: string, currentVolume: number): { isSpike: boolean; multiplier: number } {
    if (!this.volumeHistory.has(symbol)) {
      this.volumeHistory.set(symbol, []);
    }

    const history = this.volumeHistory.get(symbol)!;
    history.push(currentVolume);

    // Keep only recent history
    if (history.length > this.VOLUME_HISTORY_SIZE) {
      history.shift();
    }

    if (history.length < 5) {
      return { isSpike: false, multiplier: 1 };
    }

    // Calculate average volume (excluding current)
    const avgVolume = history.slice(0, -1).reduce((sum, vol) => sum + vol, 0) / (history.length - 1);
    const multiplier = currentVolume / avgVolume;

    // Futures volume spikes are typically 2x+ (lower threshold than spot)
    return {
      isSpike: multiplier > 2.0,
      multiplier
    };
  }

  /**
   * Analyze RSI momentum
   * Same logic as spot but adapted for futures volatility
   */
  private analyzeRSIMomentum(rsi: number, priceChange24h: number): number {
    let score = 0;

    // Oversold bounce potential (bullish)
    if (rsi < 25 && priceChange24h > 2) {
      score += 8; // Strong oversold bounce
    } else if (rsi < 35 && priceChange24h > 1) {
      score += 5; // Moderate oversold bounce
    }

    // Overbought breakdown potential (bearish)
    if (rsi > 75 && priceChange24h < -2) {
      score += 8; // Strong overbought breakdown
    } else if (rsi > 65 && priceChange24h < -1) {
      score += 5; // Moderate overbought breakdown
    }

    return score;
  }

  /**
   * Analyze EMA convergence
   * Same logic as spot implementation
   */
  private analyzeEMAConvergence(ema20: number, ema50: number, currentPrice: number): number {
    const emaSpread = Math.abs(ema20 - ema50) / currentPrice;
    const convergenceThreshold = 0.005; // 0.5%

    if (emaSpread < convergenceThreshold) {
      return 7; // Strong convergence signal
    } else if (emaSpread < convergenceThreshold * 2) {
      return 4; // Moderate convergence
    }

    return 0;
  }

  /**
   * Analyze price action for futures
   */
  private async analyzePriceAction(symbol: string): Promise<number> {
    try {
      // Get recent price data
      const ohlcv = await this.futuresService.getOHLCV(symbol, '5m', 20);
      if (ohlcv.length < 10) return 0;

      const closes = ohlcv.map(candle => candle[4]);
      const volumes = ohlcv.map(candle => candle[5]);

      // Look for price action patterns
      let score = 0;

      // Strong directional movement with volume
      const recentPriceChange = (closes[closes.length - 1] - closes[closes.length - 5]) / closes[closes.length - 5];
      const recentVolumeAvg = volumes.slice(-5).reduce((sum, vol) => sum + vol, 0) / 5;
      const historicalVolumeAvg = volumes.slice(0, -5).reduce((sum, vol) => sum + vol, 0) / (volumes.length - 5);

      if (Math.abs(recentPriceChange) > 0.02 && recentVolumeAvg > historicalVolumeAvg * 1.5) {
        score += 15; // Strong price action with volume
      }

      return score;
    } catch (error) {
      logError(`Error analyzing futures price action for ${symbol}`, error as Error);
      return 0;
    }
  }

  /**
   * Calculate overall alert from phase results
   * Uses directional scoring like spot implementation
   */
  private calculateOverallAlert(
    symbol: string,
    currentPrice: number,
    volume24h: number,
    priceChange24h: number,
    phase1: { score: number; details: any },
    phase2: { score: number; details: any },
    phase3: { score: number; details: any }
  ): FuturesEarlyWarningAlert {
    // Determine direction based on price change and technical signals
    const isPositiveDirection = priceChange24h > 0;

    // Calculate directional scores (positive for bullish, negative for bearish)
    const phase1DirectionalScore = isPositiveDirection ? phase1.score : -phase1.score;
    const phase2DirectionalScore = isPositiveDirection ? phase2.score : -phase2.score;
    const phase3DirectionalScore = isPositiveDirection ? phase3.score : -phase3.score;

    const netScore = phase1DirectionalScore + phase2DirectionalScore + phase3DirectionalScore;
    const confidence = Math.min(100, Math.abs(netScore));

    // Determine alert type
    let alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL';
    if (netScore > 15) {
      alertType = 'PUMP_LIKELY';
    } else if (netScore < -15) {
      alertType = 'DUMP_LIKELY';
    } else {
      alertType = 'NEUTRAL';
    }

    // Estimate time frame (futures moves typically faster due to leverage)
    const timeEstimateMin = Math.max(5, 30 - Math.floor(confidence / 5)); // 5-25 minutes
    const timeEstimateMax = timeEstimateMin + 15; // +15 minutes range

    // Combine triggered reasons
    const triggeredBy = [
      ...(phase1.details.triggeredBy || []),
      ...(phase2.details.triggeredBy || []),
      ...(phase3.details.triggeredBy || [])
    ];

    return {
      symbol,
      alertType,
      confidence,
      timeEstimateMin,
      timeEstimateMax,
      currentPrice,
      volume24h,
      priceChange24h,

      // Phase details
      volumeSpike: phase1.details.volumeSpike,
      rsiMomentum: phase1.details.rsiMomentum,
      emaConvergence: phase1.details.emaConvergence,
      phase1Score: Math.abs(phase1.score),

      bidAskImbalance: phase2.details.bidAskImbalance,
      priceAction: phase2.details.priceAction,
      phase2Score: Math.abs(phase2.score),

      whaleActivity: phase3.details.whaleActivity,
      phase3Score: Math.abs(phase3.score),

      triggeredBy
    };
  }

  /**
   * Check if we should create an alert (cooldown logic)
   */
  private shouldCreateAlert(symbol: string, alertType: string): boolean {
    const key = `${symbol}_${alertType}`;
    const now = Date.now();
    const lastAlert = this.lastAlertTimes.get(key);

    if (!lastAlert || (now - lastAlert) > this.ALERT_COOLDOWN_MS) {
      this.lastAlertTimes.set(key, now);
      return true;
    }

    return false;
  }

  /**
   * Get all futures symbols for analysis
   */
  async getAllFuturesSymbols(): Promise<string[]> {
    try {
      const { FUTURES_SYMBOLS } = await import('../config/futuresCoinConfig');
      return FUTURES_SYMBOLS;
    } catch (error) {
      logError('Error getting futures symbols for early warning', error as Error);
      return [];
    }
  }

  /**
   * Cleanup old data to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Clean up old alert times
    for (const [key, timestamp] of this.lastAlertTimes.entries()) {
      if (now - timestamp > maxAge) {
        this.lastAlertTimes.delete(key);
      }
    }

    // Clean up old volume history
    this.volumeHistory.clear();
    this.wallTracker.clear();

    logDebug('Futures early warning service cleanup completed');
  }
}
