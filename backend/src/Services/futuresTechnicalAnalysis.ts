import { RSI, MACD, BollingerBands, EMA } from 'technicalindicators';
import { BinanceFuturesService } from './binanceFuturesService';
import { logDebug, logError } from '../utils/logger';

export interface FuturesTechnicalIndicatorResults {
  rsi: number;
  macd: {
    MACD: number;
    signal: number;
    histogram: number;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema20: number;
  ema50: number;
  adx: number;
}

export interface FuturesChartPattern {
  name: string;
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  confidence: number;
  description: string;
}

export interface FuturesCandlestickPattern {
  name: string;
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  confidence: number;
  description: string;
}

export interface FuturesTradingSignal {
  action: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  strength: number;
  entry: number;
  stopLoss: number;
  takeProfit1: number;
  takeProfit2: number;
  takeProfit3: number;
  reasoning: string[];
}

export class FuturesTechnicalAnalysis {
  private futuresService: BinanceFuturesService;

  constructor(futuresService: BinanceFuturesService) {
    this.futuresService = futuresService;
  }

  /**
   * Calculate technical indicators for futures symbols
   * Uses the same calculation logic as spot but with futures data
   */
  async calculateIndicators(
    exchange: string,
    symbol: string,
    timeframe: string,
    limit: number = 100
  ): Promise<FuturesTechnicalIndicatorResults> {
    try {
      logDebug(`Calculating futures technical indicators for ${symbol} on ${exchange}`, {
        timeframe,
        limit,
      });

      // Get OHLCV data from futures WebSocket cache
      const ohlcv = await this.futuresService.getOHLCV(symbol, timeframe, limit);
      
      if (!ohlcv || ohlcv.length < 50) {
        throw new Error(`Insufficient futures data for ${symbol} ${timeframe}: ${ohlcv?.length || 0} candles`);
      }

      const closePrices = ohlcv.map(candle => candle[4]); // Close prices

      // Calculate RSI (same as spot)
      const rsiValues = RSI.calculate({ values: closePrices, period: 14 });
      const rsi = rsiValues[rsiValues.length - 1] || 50;

      // Calculate MACD (same as spot)
      const macdValues = MACD.calculate({
        values: closePrices,
        fastPeriod: 12,
        slowPeriod: 26,
        signalPeriod: 9,
        SimpleMAOscillator: false,
        SimpleMASignal: false
      });
      const macdLatest = macdValues[macdValues.length - 1] || { MACD: 0, signal: 0, histogram: 0 };

      // Ensure MACD values are numbers
      const macdData = {
        MACD: typeof macdLatest.MACD === 'number' ? macdLatest.MACD : 0,
        signal: typeof macdLatest.signal === 'number' ? macdLatest.signal : 0,
        histogram: typeof macdLatest.histogram === 'number' ? macdLatest.histogram : 0
      };

      // Calculate Bollinger Bands (same as spot)
      const bbValues = BollingerBands.calculate({
        values: closePrices,
        period: 20,
        stdDev: 2
      });
      const bollingerBands = bbValues[bbValues.length - 1] || { upper: 0, middle: 0, lower: 0 };

      // Calculate EMAs (same as spot)
      const ema20Values = EMA.calculate({ values: closePrices, period: 20 });
      const ema50Values = EMA.calculate({ values: closePrices, period: 50 });
      const ema20 = ema20Values[ema20Values.length - 1] || 0;
      const ema50 = ema50Values[ema50Values.length - 1] || 0;

      // Calculate ADX (same calculation as spot)
      const adx = this.calculateADX(ohlcv);

      return {
        rsi,
        macd: macdData,
        bollingerBands,
        ema20,
        ema50,
        adx
      };
    } catch (error) {
      logError(`Error calculating futures technical indicators for ${symbol}`, error as Error);
      throw error;
    }
  }

  /**
   * Calculate ADX for futures data
   * Same calculation logic as spot technical analysis
   */
  private calculateADX(ohlcv: number[][], period: number = 14): number {
    if (ohlcv.length < period + 1) return 0;

    const highs = ohlcv.map(candle => candle[2]);
    const lows = ohlcv.map(candle => candle[3]);
    const closes = ohlcv.map(candle => candle[4]);

    // Calculate True Range (TR)
    const trueRanges: number[] = [];
    for (let i = 1; i < ohlcv.length; i++) {
      const high = highs[i];
      const low = lows[i];
      const prevClose = closes[i - 1];
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trueRanges.push(tr);
    }

    // Calculate Directional Movement
    const plusDM: number[] = [];
    const minusDM: number[] = [];
    
    for (let i = 1; i < highs.length; i++) {
      const upMove = highs[i] - highs[i - 1];
      const downMove = lows[i - 1] - lows[i];
      
      plusDM.push(upMove > downMove && upMove > 0 ? upMove : 0);
      minusDM.push(downMove > upMove && downMove > 0 ? downMove : 0);
    }

    // Calculate smoothed values using Wilder's smoothing
    const smoothedPlusDM = this.wilderSmoothing(plusDM, period);
    const smoothedMinusDM = this.wilderSmoothing(minusDM, period);
    const smoothedTR = this.wilderSmoothing(trueRanges, period);

    // Calculate +DI and -DI
    const plusDI = (smoothedPlusDM / smoothedTR) * 100;
    const minusDI = (smoothedMinusDM / smoothedTR) * 100;

    // Calculate DX
    const dx = Math.abs(plusDI - minusDI) / (plusDI + minusDI) * 100;

    // Return ADX (simplified - using current DX as ADX)
    return isNaN(dx) ? 0 : dx;
  }

  /**
   * Wilder's smoothing method for ADX calculation
   * Same as spot implementation
   */
  private wilderSmoothing(values: number[], period: number): number {
    if (values.length < period) return 0;

    // First smoothed value is simple average
    let smoothed = values.slice(0, period).reduce((sum, val) => sum + val, 0) / period;

    // Apply Wilder's smoothing to remaining values
    for (let i = period; i < values.length; i++) {
      smoothed = (smoothed * (period - 1) + values[i]) / period;
    }

    return smoothed;
  }

  /**
   * Detect chart patterns in futures data
   * Same pattern detection logic as spot
   */
  detectChartPatterns(ohlcv: number[][], indicators: FuturesTechnicalIndicatorResults): FuturesChartPattern[] {
    const patterns: FuturesChartPattern[] = [];

    if (ohlcv.length < 20) return patterns;

    const closes = ohlcv.map(candle => candle[4]);
    const highs = ohlcv.map(candle => candle[2]);
    const lows = ohlcv.map(candle => candle[3]);

    // Support and Resistance levels
    const supportLevel = Math.min(...lows.slice(-20));
    const resistanceLevel = Math.max(...highs.slice(-20));
    const currentPrice = closes[closes.length - 1];

    // Double Bottom Pattern (Bullish)
    if (this.detectDoubleBottom(lows, closes)) {
      patterns.push({
        name: 'Double Bottom',
        type: 'BULLISH',
        confidence: 75,
        description: 'Price has formed a double bottom pattern, indicating potential upward reversal'
      });
    }

    // Double Top Pattern (Bearish)
    if (this.detectDoubleTop(highs, closes)) {
      patterns.push({
        name: 'Double Top',
        type: 'BEARISH',
        confidence: 75,
        description: 'Price has formed a double top pattern, indicating potential downward reversal'
      });
    }

    // Ascending Triangle (Bullish)
    if (this.detectAscendingTriangle(highs, lows)) {
      patterns.push({
        name: 'Ascending Triangle',
        type: 'BULLISH',
        confidence: 70,
        description: 'Price is forming an ascending triangle, suggesting bullish continuation'
      });
    }

    // Descending Triangle (Bearish)
    if (this.detectDescendingTriangle(highs, lows)) {
      patterns.push({
        name: 'Descending Triangle',
        type: 'BEARISH',
        confidence: 70,
        description: 'Price is forming a descending triangle, suggesting bearish continuation'
      });
    }

    // Breakout patterns
    const priceNearResistance = Math.abs(currentPrice - resistanceLevel) / resistanceLevel < 0.02;
    const priceNearSupport = Math.abs(currentPrice - supportLevel) / supportLevel < 0.02;

    if (priceNearResistance && indicators.rsi > 60) {
      patterns.push({
        name: 'Resistance Breakout Setup',
        type: 'BULLISH',
        confidence: 65,
        description: 'Price approaching resistance with strong momentum'
      });
    }

    if (priceNearSupport && indicators.rsi < 40) {
      patterns.push({
        name: 'Support Bounce Setup',
        type: 'BULLISH',
        confidence: 60,
        description: 'Price approaching support level with oversold conditions'
      });
    }

    return patterns;
  }

  /**
   * Detect candlestick patterns in futures data
   * Same candlestick pattern logic as spot
   */
  detectCandlestickPatterns(ohlcv: number[][]): FuturesCandlestickPattern[] {
    const patterns: FuturesCandlestickPattern[] = [];

    if (ohlcv.length < 3) return patterns;

    const recent = ohlcv.slice(-3); // Last 3 candles
    const [, prev1, current] = recent;

    // Doji pattern
    if (this.isDoji(current)) {
      patterns.push({
        name: 'Doji',
        type: 'NEUTRAL',
        confidence: 60,
        description: 'Indecision in the market, potential reversal signal'
      });
    }

    // Hammer pattern (Bullish)
    if (this.isHammer(current)) {
      patterns.push({
        name: 'Hammer',
        type: 'BULLISH',
        confidence: 70,
        description: 'Potential bullish reversal after downtrend'
      });
    }

    // Shooting Star pattern (Bearish)
    if (this.isShootingStar(current)) {
      patterns.push({
        name: 'Shooting Star',
        type: 'BEARISH',
        confidence: 70,
        description: 'Potential bearish reversal after uptrend'
      });
    }

    // Engulfing patterns
    if (this.isBullishEngulfing(prev1, current)) {
      patterns.push({
        name: 'Bullish Engulfing',
        type: 'BULLISH',
        confidence: 80,
        description: 'Strong bullish reversal signal'
      });
    }

    if (this.isBearishEngulfing(prev1, current)) {
      patterns.push({
        name: 'Bearish Engulfing',
        type: 'BEARISH',
        confidence: 80,
        description: 'Strong bearish reversal signal'
      });
    }

    return patterns;
  }

  /**
   * Generate trading signal for futures based on technical analysis
   * Same signal generation logic as spot but adapted for futures characteristics
   */
  generateTradingSignal(
    currentPrice: number,
    indicators: FuturesTechnicalIndicatorResults,
    chartPatterns: FuturesChartPattern[],
    candlestickPatterns: FuturesCandlestickPattern[]
  ): FuturesTradingSignal {
    let bullishScore = 0;
    let bearishScore = 0;
    const reasoning: string[] = [];

    // Technical Indicators Analysis (same weights as spot)

    // RSI Analysis
    if (indicators.rsi < 30) {
      bullishScore += 20;
      reasoning.push(`RSI oversold at ${indicators.rsi.toFixed(1)}`);
    } else if (indicators.rsi > 70) {
      bearishScore += 20;
      reasoning.push(`RSI overbought at ${indicators.rsi.toFixed(1)}`);
    }

    // MACD Analysis
    if (indicators.macd.MACD > indicators.macd.signal && indicators.macd.histogram > 0) {
      bullishScore += 15;
      reasoning.push('MACD bullish crossover');
    } else if (indicators.macd.MACD < indicators.macd.signal && indicators.macd.histogram < 0) {
      bearishScore += 15;
      reasoning.push('MACD bearish crossover');
    }

    // EMA Trend Analysis
    if (indicators.ema20 > indicators.ema50) {
      bullishScore += 10;
      reasoning.push('EMA20 above EMA50 (bullish trend)');
    } else {
      bearishScore += 10;
      reasoning.push('EMA20 below EMA50 (bearish trend)');
    }

    // Bollinger Bands Analysis
    const bbPosition = (currentPrice - indicators.bollingerBands.lower) /
                      (indicators.bollingerBands.upper - indicators.bollingerBands.lower);

    if (bbPosition < 0.2) {
      bullishScore += 10;
      reasoning.push('Price near lower Bollinger Band');
    } else if (bbPosition > 0.8) {
      bearishScore += 10;
      reasoning.push('Price near upper Bollinger Band');
    }

    // ADX Trend Strength
    if (indicators.adx > 25) {
      const trendStrengthBonus = Math.min(10, (indicators.adx - 25) / 5);
      if (bullishScore > bearishScore) {
        bullishScore += trendStrengthBonus;
        reasoning.push(`Strong trend confirmed by ADX (${indicators.adx.toFixed(1)})`);
      } else {
        bearishScore += trendStrengthBonus;
        reasoning.push(`Strong trend confirmed by ADX (${indicators.adx.toFixed(1)})`);
      }
    }

    // Chart Patterns Analysis
    chartPatterns.forEach(pattern => {
      const patternScore = pattern.confidence * 0.3; // Max 30 points per pattern
      if (pattern.type === 'BULLISH') {
        bullishScore += patternScore;
        reasoning.push(`Bullish pattern: ${pattern.name}`);
      } else if (pattern.type === 'BEARISH') {
        bearishScore += patternScore;
        reasoning.push(`Bearish pattern: ${pattern.name}`);
      }
    });

    // Candlestick Patterns Analysis
    candlestickPatterns.forEach(pattern => {
      const patternScore = pattern.confidence * 0.2; // Max 20 points per pattern
      if (pattern.type === 'BULLISH') {
        bullishScore += patternScore;
        reasoning.push(`Bullish candlestick: ${pattern.name}`);
      } else if (pattern.type === 'BEARISH') {
        bearishScore += patternScore;
        reasoning.push(`Bearish candlestick: ${pattern.name}`);
      }
    });

    // Determine signal
    const totalScore = bullishScore + bearishScore;
    const netScore = bullishScore - bearishScore;
    const confidence = Math.min(95, Math.abs(netScore));
    const strength = Math.min(100, totalScore);

    let action: 'BUY' | 'SELL' | 'HOLD';
    if (netScore > 15) {
      action = 'BUY';
    } else if (netScore < -15) {
      action = 'SELL';
    } else {
      action = 'HOLD';
    }

    // Calculate entry, stop loss, and take profit levels
    // Futures typically use tighter stops due to leverage
    const volatilityFactor = Math.abs(indicators.bollingerBands.upper - indicators.bollingerBands.lower) / currentPrice;
    const stopDistance = Math.max(0.02, volatilityFactor * 0.5); // Minimum 2% stop, max based on volatility

    let entry = currentPrice;
    let stopLoss: number;
    let takeProfit1: number;
    let takeProfit2: number;
    let takeProfit3: number;

    if (action === 'BUY') {
      stopLoss = currentPrice * (1 - stopDistance);
      takeProfit1 = currentPrice * (1 + stopDistance * 1.5);
      takeProfit2 = currentPrice * (1 + stopDistance * 2.5);
      takeProfit3 = currentPrice * (1 + stopDistance * 4);
    } else if (action === 'SELL') {
      stopLoss = currentPrice * (1 + stopDistance);
      takeProfit1 = currentPrice * (1 - stopDistance * 1.5);
      takeProfit2 = currentPrice * (1 - stopDistance * 2.5);
      takeProfit3 = currentPrice * (1 - stopDistance * 4);
    } else {
      stopLoss = currentPrice * 0.95;
      takeProfit1 = currentPrice * 1.05;
      takeProfit2 = currentPrice * 1.1;
      takeProfit3 = currentPrice * 1.15;
    }

    return {
      action,
      confidence,
      strength,
      entry,
      stopLoss,
      takeProfit1,
      takeProfit2,
      takeProfit3,
      reasoning
    };
  }

  // Pattern detection helper methods (same as spot implementation)

  private detectDoubleBottom(lows: number[], _closes: number[]): boolean {
    if (lows.length < 20) return false;

    const recentLows = lows.slice(-20);
    const minLow = Math.min(...recentLows);
    const lowIndices = recentLows.map((low, index) => ({ low, index }))
      .filter(item => Math.abs(item.low - minLow) / minLow < 0.02)
      .map(item => item.index);

    return lowIndices.length >= 2 && lowIndices[lowIndices.length - 1] - lowIndices[0] > 5;
  }

  private detectDoubleTop(highs: number[], _closes: number[]): boolean {
    if (highs.length < 20) return false;

    const recentHighs = highs.slice(-20);
    const maxHigh = Math.max(...recentHighs);
    const highIndices = recentHighs.map((high, index) => ({ high, index }))
      .filter(item => Math.abs(item.high - maxHigh) / maxHigh < 0.02)
      .map(item => item.index);

    return highIndices.length >= 2 && highIndices[highIndices.length - 1] - highIndices[0] > 5;
  }

  private detectAscendingTriangle(highs: number[], lows: number[]): boolean {
    if (highs.length < 15) return false;

    const recentHighs = highs.slice(-15);
    const recentLows = lows.slice(-15);

    // Check if highs are relatively flat (resistance)
    const highVariation = (Math.max(...recentHighs) - Math.min(...recentHighs)) / Math.max(...recentHighs);

    // Check if lows are ascending
    const firstHalfLows = recentLows.slice(0, 7);
    const secondHalfLows = recentLows.slice(8);
    const avgFirstLows = firstHalfLows.reduce((a, b) => a + b, 0) / firstHalfLows.length;
    const avgSecondLows = secondHalfLows.reduce((a, b) => a + b, 0) / secondHalfLows.length;

    return highVariation < 0.03 && avgSecondLows > avgFirstLows;
  }

  private detectDescendingTriangle(highs: number[], lows: number[]): boolean {
    if (highs.length < 15) return false;

    const recentHighs = highs.slice(-15);
    const recentLows = lows.slice(-15);

    // Check if lows are relatively flat (support)
    const lowVariation = (Math.max(...recentLows) - Math.min(...recentLows)) / Math.max(...recentLows);

    // Check if highs are descending
    const firstHalfHighs = recentHighs.slice(0, 7);
    const secondHalfHighs = recentHighs.slice(8);
    const avgFirstHighs = firstHalfHighs.reduce((a, b) => a + b, 0) / firstHalfHighs.length;
    const avgSecondHighs = secondHalfHighs.reduce((a, b) => a + b, 0) / secondHalfHighs.length;

    return lowVariation < 0.03 && avgSecondHighs < avgFirstHighs;
  }

  // Candlestick pattern detection helper methods
  private isDoji(candle: number[]): boolean {
    const [, open, high, low, close] = candle;
    const bodySize = Math.abs(close - open);
    const totalRange = high - low;
    return bodySize / totalRange < 0.1;
  }

  private isHammer(candle: number[]): boolean {
    const [, open, high, low, close] = candle;
    const bodySize = Math.abs(close - open);
    const lowerShadow = Math.min(open, close) - low;
    const upperShadow = high - Math.max(open, close);
    const totalRange = high - low;

    return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5 && bodySize / totalRange > 0.1;
  }

  private isShootingStar(candle: number[]): boolean {
    const [, open, high, low, close] = candle;
    const bodySize = Math.abs(close - open);
    const lowerShadow = Math.min(open, close) - low;
    const upperShadow = high - Math.max(open, close);
    const totalRange = high - low;

    return upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5 && bodySize / totalRange > 0.1;
  }

  private isBullishEngulfing(prev: number[], current: number[]): boolean {
    const [, prevOpen, , , prevClose] = prev;
    const [, currOpen, , , currClose] = current;

    return prevClose < prevOpen && // Previous candle was bearish
           currClose > currOpen && // Current candle is bullish
           currOpen < prevClose && // Current opens below previous close
           currClose > prevOpen;   // Current closes above previous open
  }

  private isBearishEngulfing(prev: number[], current: number[]): boolean {
    const [, prevOpen, , , prevClose] = prev;
    const [, currOpen, , , currClose] = current;

    return prevClose > prevOpen && // Previous candle was bullish
           currClose < currOpen && // Current candle is bearish
           currOpen > prevClose && // Current opens above previous close
           currClose < prevOpen;   // Current closes below previous open
  }
}
