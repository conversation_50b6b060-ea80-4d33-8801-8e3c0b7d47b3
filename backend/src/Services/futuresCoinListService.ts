import { BinanceFuturesService, BinanceFuturesTicker } from './binanceFuturesService';
import { AdvancedTechnicalAnalysis } from './advancedTechnicalAnalysis';
import { RealTimeDataService } from './realTimeDataService';
import { fetchTop50FuturesFromWebSocket } from '../config/futuresCoinConfig';
import { logDebug, logError, logInfo } from '../utils/logger';

export interface FuturesCoinListItem {
  symbol: string;
  name: string;
  price: number;
  priceChange24h: number;
  volume: number;
  lastUpdated: number;
  confidence: {
    [timeframe: string]: {
      action: 'BUY' | 'SELL' | 'HOLD';
      confidence: number;
      strength: number;
    };
  };
}

export class FuturesCoinListService {
  private futuresService: BinanceFuturesService;
  private technicalAnalysis: AdvancedTechnicalAnalysis;
  private realTimeService: RealTimeDataService | null = null;
  
  // Current futures coin list for real-time updates
  private currentFuturesCoinList: FuturesCoinListItem[] = [];
  
  // Real-time update intervals
  private realTimeUpdateInterval: NodeJS.Timeout | null = null;
  private readonly REAL_TIME_UPDATE_INTERVAL = 30000; // 30 seconds
  private readonly PRICE_BROADCAST_THROTTLE = 30000; // 30 seconds
  
  // Performance tracking
  private isProcessingRealTimeData = false;
  private lastPriceBroadcastTime = 0;
  
  // Timeframes for confidence calculation
  private readonly timeframes = ['5m', '15m', '1h', '4h', '1d'] as const;

  constructor(
    futuresService: BinanceFuturesService,
    technicalAnalysis: AdvancedTechnicalAnalysis
  ) {
    this.futuresService = futuresService;
    this.technicalAnalysis = technicalAnalysis;
    
    logInfo('🚀 Futures Coin List Service initialized');
  }

  // Set real-time service for broadcasting
  setRealTimeService(realTimeService: RealTimeDataService) {
    this.realTimeService = realTimeService;
    this.startRealTimeUpdates();
    logInfo('Real-time service connected to futures coin list service');
  }

  // Main method - Get top 50 futures coin list (mirrors spot coin list functionality)
  async getTop50FuturesCoinList(): Promise<FuturesCoinListItem[]> {
    const startTime = Date.now();

    try {
      logInfo('🔄 Fetching fresh top 50 futures coins from Binance WebSocket');

      // Get fresh top 50 futures coins with their ticker data directly from WebSocket
      const coinList = await this.generateTop50FuturesCoinListFromWebSocket();

      // Subscribe to WebSocket streams for the fetched coins
      const symbols = coinList.map(coin => coin.symbol);
      await this.subscribeToFuturesCoins(symbols);

      // Update current coin list for real-time updates and setup individual subscriptions
      this.currentFuturesCoinList = coinList;

      // Setup individual price subscriptions for each coin in the list
      this.setupFuturesCoinSpecificSubscriptions();

      const duration = Date.now() - startTime;
      logInfo(`✅ Generated and cached futures coin list for ${coinList.length} coins in ${duration}ms`);
      return coinList;

    } catch (error) {
      logError('Error getting top 50 futures coin list', error as Error);
      throw error;
    }
  }

  // Generate top 50 futures coin list from WebSocket cache
  async generateTop50FuturesCoinListFromWebSocket(): Promise<FuturesCoinListItem[]> {
    try {
      logInfo('🔄 Generating top 50 futures coin list from WebSocket cache');

      // Check if WebSocket data is ready
      if (!this.futuresService.isWebSocketDataReady()) {
        throw new Error('Futures WebSocket data not ready yet');
      }

      // Get top 50 futures symbols
      const futuresSymbols = await fetchTop50FuturesFromWebSocket();
      
      // Get all tickers from WebSocket cache
      const allTickers = this.futuresService.getAllTickersFromCache();
      
      // Filter to our top 50 symbols and sort by volume
      const top50Tickers = allTickers
        .filter(ticker => futuresSymbols.includes(ticker.symbol))
        .sort((a, b) => parseFloat(b.volume) - parseFloat(a.volume))
        .slice(0, 50);

      logInfo(`📊 Processing ${top50Tickers.length} futures coins for confidence analysis`);

      // Process coins in parallel with concurrency limit
      const concurrencyLimit = 10;
      const coinList: FuturesCoinListItem[] = [];
      
      for (let i = 0; i < top50Tickers.length; i += concurrencyLimit) {
        const batch = top50Tickers.slice(i, i + concurrencyLimit);
        
        const batchPromises = batch.map(async (ticker: BinanceFuturesTicker) => {
          try {
            const symbol = ticker.symbol;
            const price = parseFloat(ticker.price);
            const priceChange24h = parseFloat(ticker.priceChangePercent);
            const volume = parseFloat(ticker.volume);

            // Calculate confidence for all timeframes
            const confidence: { [timeframe: string]: { action: 'BUY' | 'SELL' | 'HOLD'; confidence: number; strength: number } } = {};
            
            for (const timeframe of this.timeframes) {
              try {
                // Use EXACT same calculation method as spot trading
                const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, timeframe, 100);

                // Get OHLCV data for pattern analysis (same as spot)
                const ohlcv = await this.futuresService.getOHLCV(symbol, timeframe, 100);

                // Detect patterns (same as spot)
                const chartPatterns = this.technicalAnalysis.detectChartPatterns(ohlcv, indicators);
                const candlestickPatterns = this.technicalAnalysis.detectCandlestickPatterns(ohlcv);

                // Generate trading signal using EXACT same logic as spot analysis page
                const signal = this.technicalAnalysis.generateTradingSignal(
                  price, indicators, chartPatterns, candlestickPatterns
                );

                // Convert to same format as spot (confidence and strength are already 0-100)
                confidence[timeframe] = {
                  action: signal.action,
                  confidence: Math.round(signal.confidence),
                  strength: Math.round(signal.strength)
                };
              } catch (error) {
                logError(`Error calculating futures confidence for ${symbol} ${timeframe}`, error as Error);
                confidence[timeframe] = {
                  action: 'HOLD',
                  confidence: 0,
                  strength: 0
                };
              }
            }

            return {
              symbol,
              name: symbol.replace('USDT', ''), // Remove USDT suffix for display
              price,
              priceChange24h,
              volume,
              lastUpdated: Date.now(),
              confidence
            };
          } catch (error) {
            logError(`Error processing futures coin ${ticker.symbol}`, error as Error);
            return null;
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled' && result.value) {
            coinList.push(result.value);
          }
        }
      }

      logInfo(`✅ Generated futures coin list with ${coinList.length} coins`);
      return coinList;
    } catch (error) {
      logError('Error generating futures coin list from WebSocket', error as Error);
      throw error;
    }
  }



  // Start real-time updates for futures
  private startRealTimeUpdates() {
    logInfo('Starting real-time updates for futures coin list');

    // Subscribe to futures price updates
    this.setupFuturesCoinSpecificSubscriptions();

    // Start periodic confidence updates every 30 seconds
    this.realTimeUpdateInterval = setInterval(async () => {
      if (!this.isProcessingRealTimeData && this.currentFuturesCoinList.length > 0) {
        await this.updateRealTimeConfidenceSignals();
      }
    }, this.REAL_TIME_UPDATE_INTERVAL);

    logInfo('✅ Real-time updates started for futures coin list');
  }

  // Setup coin-specific subscriptions for futures
  private setupFuturesCoinSpecificSubscriptions() {
    // This will be populated when coins are added to the current futures coin list
    logDebug('Setting up futures coin-specific WebSocket subscriptions');
  }

  // Subscribe to futures coins for real-time updates
  async subscribeToFuturesCoins(symbols: string[]) {
    logInfo(`🔗 Subscribing to real-time updates for ${symbols.length} futures coins`);

    symbols.forEach(symbol => {
      this.futuresService.subscribeToPrice(symbol, async (data: BinanceFuturesTicker) => {
        await this.processFuturesPriceUpdate(symbol, data);
      });
    });

    logInfo(`✅ Subscribed to real-time futures data for: ${symbols.join(', ')}`);
  }

  // Process individual futures price updates
  private async processFuturesPriceUpdate(symbol: string, data: BinanceFuturesTicker) {
    try {
      // Update the coin in current list
      const coinIndex = this.currentFuturesCoinList.findIndex(coin => coin.symbol === symbol);
      if (coinIndex !== -1) {
        const coin = this.currentFuturesCoinList[coinIndex];
        coin.price = parseFloat(data.price);
        coin.priceChange24h = parseFloat(data.priceChangePercent);
        coin.volume = parseFloat(data.volume);
        coin.lastUpdated = Date.now();

        // Throttled broadcasting (every 30 seconds)
        const now = Date.now();
        const shouldBroadcast = (now - this.lastPriceBroadcastTime) >= this.PRICE_BROADCAST_THROTTLE;

        if (this.realTimeService && shouldBroadcast) {
          // Broadcast individual futures price update
          this.realTimeService.broadcastFuturesCoinPriceUpdate(
            coin.symbol,
            coin.price,
            coin.priceChange24h,
            coin.volume
          );

          // Broadcast full futures coin list update
          this.realTimeService.broadcastFuturesCoinListUpdate(this.currentFuturesCoinList);

          // Update last broadcast time
          this.lastPriceBroadcastTime = now;

          logDebug(`Broadcasted futures price updates (30s throttle) - ${symbol}: $${coin.price.toFixed(6)} (${coin.priceChange24h.toFixed(2)}%)`);
        } else {
          logDebug(`Updated futures price (no broadcast - throttled) for ${symbol}: $${coin.price.toFixed(6)} (${coin.priceChange24h.toFixed(2)}%)`);
        }
      }
    } catch (error) {
      logError(`Error processing futures price update for ${symbol}`, error as Error);
    }
  }

  // Update real-time confidence signals for futures
  private async updateRealTimeConfidenceSignals() {
    if (this.isProcessingRealTimeData) return;

    try {
      this.isProcessingRealTimeData = true;
      logDebug(`🔄 Updating real-time confidence signals for ${this.currentFuturesCoinList.length} futures coins`);

      // Process coins in batches for better performance
      const batchSize = 5;
      for (let i = 0; i < this.currentFuturesCoinList.length; i += batchSize) {
        const batch = this.currentFuturesCoinList.slice(i, i + batchSize);

        const batchPromises = batch.map(async (coin) => {
          try {
            // Generate confidence using WebSocket cached data (no REST API calls)
            const newConfidence = await this.generateRealTimeConfidenceSignals(coin.symbol);
            coin.confidence = newConfidence;
            coin.lastUpdated = Date.now();

            // Broadcast individual futures confidence update
            if (this.realTimeService) {
              this.realTimeService.broadcastFuturesCoinConfidenceUpdate(coin);
            }
          } catch (error) {
            logDebug(`Failed to update futures confidence for ${coin.symbol}:`, error);
          }
        });

        await Promise.allSettled(batchPromises);
      }

      // Broadcast full futures coin list update
      if (this.realTimeService) {
        this.realTimeService.broadcastFuturesCoinListUpdate(this.currentFuturesCoinList);
      }

      logDebug('✅ Real-time futures confidence signals updated');
    } catch (error) {
      logError('Error updating real-time futures confidence signals', error as Error);
    } finally {
      this.isProcessingRealTimeData = false;
    }
  }

  // Generate real-time confidence signals for a futures symbol
  private async generateRealTimeConfidenceSignals(symbol: string): Promise<{ [timeframe: string]: { action: 'BUY' | 'SELL' | 'HOLD'; confidence: number; strength: number } }> {
    const confidence: { [timeframe: string]: { action: 'BUY' | 'SELL' | 'HOLD'; confidence: number; strength: number } } = {};

    // Get current price for the symbol
    const currentPrice = await this.futuresService.getCurrentPrice(symbol);

    for (const timeframe of this.timeframes) {
      try {
        // Use EXACT same calculation method as spot trading
        const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, timeframe, 100);

        // Get OHLCV data for pattern analysis (same as spot)
        const ohlcv = await this.futuresService.getOHLCV(symbol, timeframe, 100);

        // Detect patterns (same as spot)
        const chartPatterns = this.technicalAnalysis.detectChartPatterns(ohlcv, indicators);
        const candlestickPatterns = this.technicalAnalysis.detectCandlestickPatterns(ohlcv);

        // Generate trading signal using EXACT same logic as spot analysis page
        const signal = this.technicalAnalysis.generateTradingSignal(
          currentPrice, indicators, chartPatterns, candlestickPatterns
        );

        // Convert to same format as spot (confidence and strength are already 0-100)
        confidence[timeframe] = {
          action: signal.action,
          confidence: Math.round(signal.confidence),
          strength: Math.round(signal.strength)
        };
      } catch (error) {
        logDebug(`Error calculating real-time futures confidence for ${symbol} ${timeframe}:`, error);
        confidence[timeframe] = {
          action: 'HOLD',
          confidence: 0,
          strength: 0
        };
      }
    }

    return confidence;
  }

  // Get current futures coin list
  getCurrentFuturesCoinList(): FuturesCoinListItem[] {
    return this.currentFuturesCoinList;
  }

  // Set current futures coin list and start real-time tracking
  async setCurrentFuturesCoinList(coinList: FuturesCoinListItem[]) {
    this.currentFuturesCoinList = coinList;
    
    // Subscribe to real-time updates for these coins
    const symbols = coinList.map(coin => coin.symbol);
    await this.subscribeToFuturesCoins(symbols);
    
    logInfo(`✅ Set current futures coin list with ${coinList.length} coins and started real-time tracking`);
  }

  // Stop real-time updates
  stopRealTimeUpdates() {
    if (this.realTimeUpdateInterval) {
      clearInterval(this.realTimeUpdateInterval);
      this.realTimeUpdateInterval = null;
    }
    logInfo('🛑 Stopped real-time updates for futures coin list');
  }
}
