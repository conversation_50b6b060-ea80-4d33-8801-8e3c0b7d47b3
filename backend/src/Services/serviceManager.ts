import { BinanceService } from './binanceService';
import { BinanceFuturesService } from './binanceFuturesService';
import { AdvancedTechnicalAnalysis } from './advancedTechnicalAnalysis';
import { FuturesTechnicalAnalysis } from './futuresTechnicalAnalysis';
import { FuturesCoinListService } from './futuresCoinListService';
import { CoinGeckoService } from './coinGeckoService';
import { logInfo, logError } from '../utils/logger';

// No pre-subscription at startup - coins will be subscribed only when user visits coin-list page

/**
 * Service Manager - Singleton pattern to ensure all parts of the application
 * use the same WebSocket-enabled service instances
 */
class ServiceManager {
  private static instance: ServiceManager;
  private _binanceService: BinanceService | null = null;
  private _binanceFuturesService: BinanceFuturesService | null = null;
  private _technicalAnalysisService: AdvancedTechnicalAnalysis | null = null;
  private _futuresTechnicalAnalysisService: FuturesTechnicalAnalysis | null = null;
  private _futuresCoinListService: FuturesCoinListService | null = null;
  private _coinGeckoService: CoinGeckoService | null = null;
  private _initialized = false;

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  // Initialize all services with WebSocket subscriptions
  public async initialize(): Promise<void> {
    if (this._initialized) {
      return;
    }

    try {
      logInfo('Initializing shared services with WebSocket support');

      // Create BinanceService instance
      this._binanceService = new BinanceService();

      // Create BinanceFuturesService instance
      this._binanceFuturesService = new BinanceFuturesService();

      // Create AdvancedTechnicalAnalysis with shared BinanceService
      this._technicalAnalysisService = new AdvancedTechnicalAnalysis(this._binanceService);

      // Create FuturesTechnicalAnalysis with shared BinanceFuturesService
      this._futuresTechnicalAnalysisService = new FuturesTechnicalAnalysis(this._binanceFuturesService);

      // Create FuturesCoinListService with shared services (using same technical analysis as spot)
      this._futuresCoinListService = new FuturesCoinListService(this._binanceFuturesService, this._technicalAnalysisService);

      // Create CoinGeckoService
      this._coinGeckoService = new CoinGeckoService();

      // No pre-subscription at startup - WebSocket streams will be created dynamically
      // when user visits coin-list page and fresh top 50 coins are fetched

      this._initialized = true;
      logInfo('Successfully initialized shared services with WebSocket support');

      // Log cache statistics
      const stats = (this._binanceService as any).getKlineCacheStats();
      logInfo(`Shared service kline cache: ${stats.totalStreams} streams, ${stats.totalCandles} total candles, ${stats.symbols.length} symbols`);
    } catch (error) {
      logError('Failed to initialize shared services', error as Error);
      throw error;
    }
  }

  // Get BinanceService instance
  public getBinanceService(): BinanceService {
    if (!this._binanceService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._binanceService;
  }

  // Get BinanceFuturesService instance
  public getBinanceFuturesService(): BinanceFuturesService {
    if (!this._binanceFuturesService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._binanceFuturesService;
  }

  // Get AdvancedTechnicalAnalysis instance
  public getTechnicalAnalysisService(): AdvancedTechnicalAnalysis {
    if (!this._technicalAnalysisService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._technicalAnalysisService;
  }

  // Get FuturesTechnicalAnalysis instance
  public getFuturesTechnicalAnalysisService(): FuturesTechnicalAnalysis {
    if (!this._futuresTechnicalAnalysisService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._futuresTechnicalAnalysisService;
  }

  // Get FuturesCoinListService instance
  public getFuturesCoinListService(): FuturesCoinListService {
    if (!this._futuresCoinListService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._futuresCoinListService;
  }

  // Get CoinGeckoService instance
  public getCoinGeckoService(): CoinGeckoService {
    if (!this._coinGeckoService) {
      throw new Error('ServiceManager not initialized. Call initialize() first.');
    }
    return this._coinGeckoService;
  }

  // Check if services are initialized
  public isInitialized(): boolean {
    return this._initialized;
  }

  // Get service statistics
  public getStats(): {
    initialized: boolean;
    klineCache?: { totalStreams: number; totalCandles: number; symbols: string[] };
  } {
    const stats: any = {
      initialized: this._initialized
    };

    if (this._binanceService) {
      stats.klineCache = (this._binanceService as any).getKlineCacheStats();
    }

    return stats;
  }
}

// Export singleton instance
export const serviceManager = ServiceManager.getInstance();

// Export convenience functions
export const getBinanceService = (): BinanceService => serviceManager.getBinanceService();
export const getBinanceFuturesService = (): BinanceFuturesService => serviceManager.getBinanceFuturesService();
export const getTechnicalAnalysisService = (): AdvancedTechnicalAnalysis => serviceManager.getTechnicalAnalysisService();
export const getFuturesTechnicalAnalysisService = (): FuturesTechnicalAnalysis => serviceManager.getFuturesTechnicalAnalysisService();
export const getFuturesCoinListService = (): FuturesCoinListService => serviceManager.getFuturesCoinListService();
export const getCoinGeckoService = (): CoinGeckoService => serviceManager.getCoinGeckoService();
