import axios from 'axios';
import WebSocket from 'ws';
import { logDebug, logError, logInfo } from '../utils/logger';
import { config } from '../config/config';

export interface BinanceFuturesSymbol {
  symbol: string;
  status: string;
  baseAsset: string;
  quoteAsset: string;
  marginAsset: string;
  pricePrecision: number;
  quantityPrecision: number;
  baseAssetPrecision: number;
  quotePrecision: number;
  contractType: string;
  deliveryDate: number;
  onboardDate: number;
}

export interface BinanceFuturesTicker {
  symbol: string;
  price: string;
  priceChangePercent: string;
  volume: string; // Quote asset volume (USDT value)
  baseVolume: string; // Base asset volume (coin count)
  high: string;
  low: string;
  openPrice: string;
  closePrice: string;
  count: string; // Trade count
}

export interface FuturesOrderBookData {
  symbol: string;
  bids: Array<{ price: number; quantity: number }>;
  asks: Array<{ price: number; quantity: number }>;
  lastUpdateId: number;
  timestamp: number;
}

export class BinanceFuturesService {
  private baseURL = 'https://fapi.binance.com/fapi/v1';
  private wsBaseURL = 'wss://fstream.binance.com/ws';
  private combinedStreamURL = 'wss://fstream.binance.com/stream';

  // Combined WebSocket connections for futures
  private combinedTickerWS: WebSocket | null = null;
  private combinedKlineWS: Map<string, WebSocket> = new Map(); // One per timeframe

  // Legacy individual connections (kept for compatibility)
  private wsConnections: Map<string, WebSocket> = new Map();

  private allTickersCache: Map<string, BinanceFuturesTicker> = new Map();
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private isAllTickersConnected = false;
  private allTickersSubscribers: Set<(tickers: BinanceFuturesTicker[]) => void> = new Set();

  // Kline data caching for WebSocket streams
  private klineCache: Map<string, number[][]> = new Map(); // key: symbol_timeframe, value: OHLCVBS array
  private klineSubscribers: Map<string, Set<(ohlcv: number[][]) => void>> = new Map();
  private klineConnections: Map<string, WebSocket> = new Map();
  private readonly MAX_KLINE_BUFFER = 100; // Keep 100 candles in buffer

  // Order book data caching for WebSocket streams
  private orderBookCache: Map<string, FuturesOrderBookData> = new Map();
  private orderBookSubscribers: Map<string, Set<(orderBook: FuturesOrderBookData) => void>> = new Map();
  private orderBookConnections: Map<string, WebSocket> = new Map();

  // Track API usage statistics
  private cacheHits = 0;
  private restApiCalls = 0;

  // Tracked symbols for combined streams
  private trackedSymbols: Set<string> = new Set();
  private activeTimeframes: Set<string> = new Set(['5m', '15m', '1h', '4h', '1d']);

  // Smart reconnection state
  private reconnectionState: Map<string, { attempts: number; lastAttempt: number; backoffMs: number }> = new Map();
  private readonly MAX_RECONNECTION_ATTEMPTS = 10;
  private readonly BASE_BACKOFF_MS = 1000;
  private readonly MAX_BACKOFF_MS = 30000;

  constructor() {
    logInfo('🚀 Initializing Binance Futures Service with WebSocket-only architecture');
    this.initializeCombinedStreams();
  }

  // Initialize combined streams for futures
  private async initializeCombinedStreams() {
    try {
      logInfo('🚀 Initializing Binance Futures Combined Streams (6 connections instead of 150+)');

      // Import the futures coins list (will be created next)
      const { fetchTop50FuturesFromWebSocket } = await import('../config/futuresCoinConfig');
      const futuresCoins = await fetchTop50FuturesFromWebSocket();

      // Add all futures coins to tracking
      futuresCoins.forEach(symbol => this.trackedSymbols.add(symbol));

      // Initialize combined ticker stream for all tracked symbols
      await this.initializeCombinedTickerStream();

      // Initialize combined kline streams for each timeframe
      for (const timeframe of this.activeTimeframes) {
        await this.initializeCombinedKlineStream(timeframe);
      }

      logInfo(`✅ Futures combined streams initialized for ${futuresCoins.length} symbols across ${this.activeTimeframes.size} timeframes`);
    } catch (error) {
      logError('Failed to initialize futures combined streams', error as Error);
    }
  }

  // Initialize combined ticker stream for futures
  private async initializeCombinedTickerStream() {
    const symbols = Array.from(this.trackedSymbols);
    if (symbols.length === 0) return;

    const streams = symbols.map(symbol => `${symbol.toLowerCase()}@ticker`);
    const streamUrl = `${this.combinedStreamURL}?streams=${streams.join('/')}`;

    logInfo(`🔌 Connecting to futures combined ticker stream for ${symbols.length} symbols`);

    const ws = new WebSocket(streamUrl);
    const connectionKey = 'futures_combined_ticker';

    ws.on('open', () => {
      logInfo(`✅ Connected to futures combined ticker stream for ${symbols.length} symbols`);
      this.isAllTickersConnected = true;
      this.resetReconnectionState(connectionKey);
    });

    ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleCombinedTickerMessage(message);
      } catch (error) {
        logError('Error parsing futures combined ticker message', error as Error);
      }
    });

    ws.on('error', (error) => {
      logError('Futures combined ticker WebSocket error', error);
    });

    ws.on('close', () => {
      logInfo('Futures combined ticker WebSocket closed, attempting smart reconnection...');
      this.isAllTickersConnected = false;
      this.combinedTickerWS = null;
      this.attemptSmartReconnection(connectionKey, () => this.initializeCombinedTickerStream());
    });

    this.combinedTickerWS = ws;
  }

  // Initialize combined kline stream for specific timeframe
  private async initializeCombinedKlineStream(timeframe: string) {
    const symbols = Array.from(this.trackedSymbols);
    if (symbols.length === 0) return;

    const interval = this.convertTimeframeToInterval(timeframe);
    const streams = symbols.map(symbol => `${symbol.toLowerCase()}@kline_${interval}`);
    const streamUrl = `${this.combinedStreamURL}?streams=${streams.join('/')}`;

    logInfo(`🔌 Connecting to futures combined ${timeframe} kline stream for ${symbols.length} symbols`);

    const ws = new WebSocket(streamUrl);
    const connectionKey = `futures_combined_kline_${timeframe}`;

    ws.on('open', () => {
      logInfo(`✅ Connected to futures combined ${timeframe} kline stream for ${symbols.length} symbols`);
      this.resetReconnectionState(connectionKey);
    });

    ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleCombinedKlineMessage(message, timeframe);
      } catch (error) {
        logError(`Error parsing futures combined ${timeframe} kline message`, error as Error);
      }
    });

    ws.on('error', (error) => {
      logError(`Futures combined ${timeframe} kline WebSocket error`, error);
    });

    ws.on('close', () => {
      logInfo(`Futures combined ${timeframe} kline WebSocket closed, attempting smart reconnection...`);
      this.combinedKlineWS.delete(timeframe);
      this.attemptSmartReconnection(connectionKey, () => this.initializeCombinedKlineStream(timeframe));
    });

    this.combinedKlineWS.set(timeframe, ws);
  }

  // Handle combined ticker messages
  private handleCombinedTickerMessage(message: any) {
    if (message.stream && message.data) {
      const ticker = message.data;
      
      if (ticker.s && ticker.s.endsWith('USDT')) {
        const tickerData: BinanceFuturesTicker = {
          symbol: ticker.s,
          price: ticker.c,
          priceChangePercent: ticker.P,
          volume: ticker.q, // Quote asset volume (USDT value)
          baseVolume: ticker.v, // Base asset volume (coin count)
          high: ticker.h,
          low: ticker.l,
          openPrice: ticker.o,
          closePrice: ticker.c,
          count: ticker.n
        };

        // Update cache
        this.allTickersCache.set(ticker.s, tickerData);

        // Notify individual subscribers
        this.notifySubscribers(ticker.s, tickerData);

        // Notify all-tickers subscribers
        this.notifyAllTickersSubscribers();
      }
    }
  }

  // Handle combined kline messages
  private handleCombinedKlineMessage(message: any, timeframe: string) {
    if (message.stream && message.data) {
      const klineData = message.data.k;
      if (!klineData) return;

      const symbol = klineData.s;
      const key = `${symbol}_${timeframe}`;

      // Only process closed candles for historical data
      if (klineData.x) {
        const ohlcvData = [
          klineData.t, // Open time
          parseFloat(klineData.o), // Open
          parseFloat(klineData.h), // High
          parseFloat(klineData.l), // Low
          parseFloat(klineData.c), // Close
          parseFloat(klineData.v), // Volume
          parseFloat(klineData.V), // Taker buy base asset volume
          parseFloat(klineData.q) - parseFloat(klineData.V) // Taker sell base asset volume
        ];

        this.updateKlineCache(key, ohlcvData);
        this.notifyKlineSubscribers(key);
      }
    }
  }

  // Convert timeframe to Binance interval format
  private convertTimeframeToInterval(timeframe: string): string {
    const mapping: { [key: string]: string } = {
      '1m': '1m',
      '5m': '5m',
      '15m': '15m',
      '30m': '30m',
      '1h': '1h',
      '4h': '4h',
      '1d': '1d'
    };
    return mapping[timeframe] || '1h';
  }

  // Update kline cache with new data
  private updateKlineCache(key: string, newCandle: number[]) {
    if (!this.klineCache.has(key)) {
      this.klineCache.set(key, []);
    }

    const cache = this.klineCache.get(key)!;
    cache.push(newCandle);

    // Keep only the last MAX_KLINE_BUFFER candles
    if (cache.length > this.MAX_KLINE_BUFFER) {
      cache.splice(0, cache.length - this.MAX_KLINE_BUFFER);
    }
  }

  // Notify kline subscribers
  private notifyKlineSubscribers(key: string) {
    const subscribers = this.klineSubscribers.get(key);
    if (subscribers && subscribers.size > 0) {
      const cachedData = this.klineCache.get(key) || [];
      subscribers.forEach(callback => {
        try {
          callback(cachedData);
        } catch (error) {
          logError(`Error notifying kline subscriber for ${key}`, error as Error);
        }
      });
    }
  }

  // Notify individual price subscribers
  private notifySubscribers(symbol: string, data: BinanceFuturesTicker) {
    const subscribers = this.subscribers.get(symbol);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logError(`Error notifying subscriber for ${symbol}`, error as Error);
        }
      });
    }
  }

  // Notify all-tickers subscribers
  private notifyAllTickersSubscribers() {
    if (this.allTickersSubscribers.size > 0) {
      const tickers = this.getAllTickersFromCache();
      this.allTickersSubscribers.forEach(callback => {
        try {
          callback(tickers);
        } catch (error) {
          logError('Error notifying all-tickers subscriber', error as Error);
        }
      });
    }
  }

  // Get all tickers from WebSocket cache
  getAllTickersFromCache(): BinanceFuturesTicker[] {
    const tickers = Array.from(this.allTickersCache.values())
      .filter(ticker =>
        ticker.symbol.endsWith('USDT') &&
        !ticker.symbol.includes('UP') &&
        !ticker.symbol.includes('DOWN') &&
        !ticker.symbol.includes('BEAR') &&
        !ticker.symbol.includes('BULL')
      )
      .sort((a: BinanceFuturesTicker, b: BinanceFuturesTicker) =>
        parseFloat(b.volume) - parseFloat(a.volume)
      );

    logDebug(`Retrieved ${tickers.length} futures tickers from WebSocket cache`);
    return tickers;
  }

  // Check if WebSocket data is ready
  isWebSocketDataReady(): boolean {
    return this.isAllTickersConnected && this.allTickersCache.size > 0;
  }

  // Subscribe to price updates for a specific symbol
  subscribeToPrice(symbol: string, callback: (data: BinanceFuturesTicker) => void) {
    if (!this.subscribers.has(symbol)) {
      this.subscribers.set(symbol, new Set());
      // Add symbol to tracked symbols for combined streams
      this.addTrackedSymbols([symbol]);
    }
    this.subscribers.get(symbol)!.add(callback);

    // Send current cached data if available
    const cached = this.allTickersCache.get(symbol);
    if (cached) {
      callback(cached);
    } else {
      logDebug(`No cached data for ${symbol} yet - futures WebSocket streams will provide data when available`);
    }
  }

  // Unsubscribe from price updates
  unsubscribeFromPrice(symbol: string, callback: (data: BinanceFuturesTicker) => void) {
    const subscribers = this.subscribers.get(symbol);
    if (subscribers) {
      subscribers.delete(callback);
      if (subscribers.size === 0) {
        this.subscribers.delete(symbol);
      }
    }
  }

  // Subscribe to all tickers updates
  subscribeToAllTickers(callback: (tickers: BinanceFuturesTicker[]) => void) {
    this.allTickersSubscribers.add(callback);

    // Send current data if available
    if (this.isWebSocketDataReady()) {
      callback(this.getAllTickersFromCache());
    }
  }

  // Unsubscribe from all tickers updates
  unsubscribeFromAllTickers(callback: (tickers: BinanceFuturesTicker[]) => void) {
    this.allTickersSubscribers.delete(callback);
  }

  // Get current price for a symbol (WebSocket cache only)
  async getCurrentPrice(symbol: string): Promise<number> {
    try {
      const cached = this.allTickersCache.get(symbol);
      if (cached) {
        return parseFloat(cached.price);
      }

      logError(`No cached price data available for futures ${symbol} - WebSocket may not have received data yet`);
      throw new Error(`No cached price data available for futures ${symbol}`);
    } catch (error) {
      logError(`Error getting cached price for futures ${symbol}`, error as Error);
      throw error;
    }
  }

  // Get cached price data
  getCachedPrice(symbol: string): BinanceFuturesTicker | null {
    return this.allTickersCache.get(symbol) || null;
  }

  // Subscribe to kline data for technical analysis
  async subscribeToKlineData(symbol: string, timeframe: string, callback?: (ohlcv: number[][]) => void): Promise<void> {
    const key = `${symbol}_${timeframe}`;

    if (!this.klineSubscribers.has(key)) {
      this.klineSubscribers.set(key, new Set());
    }

    if (callback) {
      this.klineSubscribers.get(key)!.add(callback);
    }

    try {
      // First, get initial historical data via REST API (one-time only)
      await this.initializeKlineBuffer(symbol, timeframe);

      // Then start WebSocket stream for real-time updates
      this.startKlineWebSocketStream(symbol, timeframe);

      logInfo(`Successfully subscribed to futures kline data for ${symbol} ${timeframe}`);
    } catch (error) {
      logError(`Failed to subscribe to futures kline data for ${symbol} ${timeframe}`, error as Error);
      throw error;
    }
  }

  // Initialize kline buffer with historical data
  private async initializeKlineBuffer(symbol: string, timeframe: string): Promise<void> {
    const key = `${symbol}_${timeframe}`;

    // Skip if already initialized
    if (this.klineCache.has(key) && this.klineCache.get(key)!.length > 0) {
      logDebug(`Kline buffer already initialized for futures ${symbol} ${timeframe}`);
      return;
    }

    try {
      const interval = this.convertTimeframeToInterval(timeframe);
      const klines = await this.getKlines(symbol, interval, this.MAX_KLINE_BUFFER);

      const ohlcvData = klines.map(kline => [
        kline.openTime,
        parseFloat(kline.open),
        parseFloat(kline.high),
        parseFloat(kline.low),
        parseFloat(kline.close),
        parseFloat(kline.volume),
        parseFloat(kline.takerBuyBaseAssetVolume),
        parseFloat(kline.volume) - parseFloat(kline.takerBuyBaseAssetVolume) // Sell volume
      ]);

      this.klineCache.set(key, ohlcvData);
      logInfo(`Initialized futures kline buffer for ${symbol} ${timeframe} with ${ohlcvData.length} candles`);
    } catch (error) {
      logError(`Failed to initialize futures kline buffer for ${symbol} ${timeframe}`, error as Error);
      throw error;
    }
  }

  // Start WebSocket stream for real-time kline updates
  private startKlineWebSocketStream(symbol: string, timeframe: string): void {
    const key = `${symbol}_${timeframe}`;
    const interval = this.convertTimeframeToInterval(timeframe);
    const wsUrl = `${this.wsBaseURL}/${symbol.toLowerCase()}@kline_${interval}`;

    const ws = new WebSocket(wsUrl);

    ws.on('open', () => {
      logDebug(`Connected to futures kline WebSocket for ${symbol} ${timeframe}`);
    });

    ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleKlineUpdate(symbol, timeframe, message);
      } catch (error) {
        logError(`Error parsing futures kline WebSocket message for ${symbol} ${timeframe}`, error as Error);
      }
    });

    ws.on('error', (error) => {
      logError(`Futures kline WebSocket error for ${symbol} ${timeframe}`, error);
    });

    ws.on('close', () => {
      logInfo(`Futures kline WebSocket connection closed for ${symbol} ${timeframe}, attempting smart reconnection...`);
      this.klineConnections.delete(key);

      this.attemptSmartReconnection(`futures_kline_${key}`, () => {
        if (!this.klineConnections.has(key)) {
          this.startKlineWebSocketStream(symbol, timeframe);
        }
      });
    });

    this.klineConnections.set(key, ws);
  }

  // Handle incoming kline updates from WebSocket
  private handleKlineUpdate(symbol: string, timeframe: string, message: any): void {
    const key = `${symbol}_${timeframe}`;
    const klineData = message.k;

    if (!klineData) {
      logError(`Invalid kline data received for futures ${symbol} ${timeframe}`, new Error('Missing kline data'));
      return;
    }

    // Only process closed candles for historical data
    if (klineData.x) {
      const ohlcvData = [
        klineData.t, // Open time
        parseFloat(klineData.o), // Open
        parseFloat(klineData.h), // High
        parseFloat(klineData.l), // Low
        parseFloat(klineData.c), // Close
        parseFloat(klineData.v), // Volume
        parseFloat(klineData.V), // Taker buy base asset volume
        parseFloat(klineData.v) - parseFloat(klineData.V) // Taker sell base asset volume
      ];

      this.updateKlineCache(key, ohlcvData);
      this.notifyKlineSubscribers(key);

      logDebug(`Updated futures kline cache for ${symbol} ${timeframe}: Close=${klineData.c}, Volume=${klineData.v}`);
    }
  }

  // Add symbols to be tracked via combined WebSocket streams
  public addTrackedSymbols(symbols: string[]): void {
    const newSymbols: string[] = [];

    symbols.forEach(symbol => {
      if (!this.trackedSymbols.has(symbol)) {
        this.trackedSymbols.add(symbol);
        newSymbols.push(symbol);
      }
    });

    if (newSymbols.length > 0) {
      logInfo(`Added ${newSymbols.length} new futures symbols to tracking: ${newSymbols.join(', ')}`);
      // Note: In a production system, you might want to restart combined streams
      // For now, new symbols will be picked up on next reconnection
    }
  }

  // Get OHLCV data - uses WebSocket cache instead of REST API
  async getOHLCV(
    symbol: string,
    timeframe: string,
    limit: number = 100
  ): Promise<number[][]> {
    try {
      // First try to get from WebSocket cache
      if (this.hasKlineData(symbol, timeframe)) {
        const cachedData = this.getCachedOHLCV(symbol, timeframe, limit);
        this.cacheHits++;
        logDebug(`✅ Retrieved ${cachedData.length} futures OHLCV candles from WebSocket cache for ${symbol} ${timeframe} - NO REST API CALL! (Cache hits: ${this.cacheHits})`);
        return cachedData;
      }

      // If not in cache, subscribe to WebSocket and get initial data
      logDebug(`No cached data for futures ${symbol} ${timeframe}, initializing WebSocket subscription`);
      await this.subscribeToKlineData(symbol, timeframe);

      // Get the newly cached data
      const cachedData = this.getCachedOHLCV(symbol, timeframe, limit);
      if (cachedData.length > 0) {
        logDebug(`Retrieved ${cachedData.length} futures OHLCV candles after WebSocket initialization for ${symbol} ${timeframe}`);
        return cachedData;
      }

      // Fallback to REST API if WebSocket fails (should be rare)
      this.restApiCalls++;
      logError(`⚠️ Futures WebSocket cache failed for ${symbol} ${timeframe}, falling back to REST API - THIS SHOULD BE RARE! (REST API calls: ${this.restApiCalls})`, new Error('WebSocket cache unavailable'));
      const interval = this.convertTimeframeToInterval(timeframe);
      const klines = await this.getKlines(symbol, interval, limit);

      return klines.map(kline => [
        kline.openTime,
        parseFloat(kline.open),
        parseFloat(kline.high),
        parseFloat(kline.low),
        parseFloat(kline.close),
        parseFloat(kline.volume),
        parseFloat(kline.takerBuyBaseAssetVolume || '0'),
        parseFloat(kline.volume) - parseFloat(kline.takerBuyBaseAssetVolume || '0')
      ]);
    } catch (error) {
      logError(`Error getting futures OHLCV data for ${symbol} ${timeframe}`, error as Error);
      throw error;
    }
  }

  // Check if kline data exists in cache
  private hasKlineData(symbol: string, timeframe: string): boolean {
    const key = `${symbol}_${timeframe}`;
    const cached = this.klineCache.get(key);
    return cached !== undefined && cached.length > 0;
  }

  // Get cached OHLCV data
  private getCachedOHLCV(symbol: string, timeframe: string, limit: number): number[][] {
    const key = `${symbol}_${timeframe}`;
    const cached = this.klineCache.get(key) || [];

    // Return the last 'limit' candles
    return cached.slice(-limit);
  }

  // Get klines from REST API (fallback only)
  private async getKlines(symbol: string, interval: string, limit: number = 100): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseURL}/klines`, {
        params: {
          symbol,
          interval,
          limit
        }
      });

      return response.data.map((kline: any[]) => ({
        openTime: kline[0],
        open: kline[1],
        high: kline[2],
        low: kline[3],
        close: kline[4],
        volume: kline[5],
        closeTime: kline[6],
        quoteAssetVolume: kline[7],
        numberOfTrades: kline[8],
        takerBuyBaseAssetVolume: kline[9],
        takerBuyQuoteAssetVolume: kline[10]
      }));
    } catch (error) {
      logError(`Error fetching futures klines for ${symbol}`, error as Error);
      throw error;
    }
  }

  // Smart reconnection logic
  private attemptSmartReconnection(connectionKey: string, reconnectFn: () => void): void {
    const state = this.reconnectionState.get(connectionKey) || {
      attempts: 0,
      lastAttempt: 0,
      backoffMs: this.BASE_BACKOFF_MS
    };

    const now = Date.now();

    // Check if we've exceeded max attempts
    if (state.attempts >= this.MAX_RECONNECTION_ATTEMPTS) {
      logError(`Max reconnection attempts reached for futures ${connectionKey}`, new Error('Max reconnection attempts exceeded'));
      return;
    }

    // Calculate backoff delay
    const timeSinceLastAttempt = now - state.lastAttempt;
    if (timeSinceLastAttempt < state.backoffMs) {
      // Schedule reconnection after backoff period
      setTimeout(() => {
        this.attemptSmartReconnection(connectionKey, reconnectFn);
      }, state.backoffMs - timeSinceLastAttempt);
      return;
    }

    // Attempt reconnection
    state.attempts++;
    state.lastAttempt = now;
    state.backoffMs = Math.min(state.backoffMs * 2, this.MAX_BACKOFF_MS);

    this.reconnectionState.set(connectionKey, state);

    logInfo(`Attempting futures reconnection ${state.attempts}/${this.MAX_RECONNECTION_ATTEMPTS} for ${connectionKey} (backoff: ${state.backoffMs}ms)`);

    try {
      reconnectFn();
    } catch (error) {
      logError(`Futures reconnection attempt failed for ${connectionKey}`, error as Error);
    }
  }

  // Reset reconnection state on successful connection
  private resetReconnectionState(connectionKey: string): void {
    this.reconnectionState.delete(connectionKey);
    logDebug(`Reset futures reconnection state for ${connectionKey}`);
  }

  // Close all WebSocket connections
  closeConnections() {
    // Close combined streams
    if (this.combinedTickerWS) {
      this.combinedTickerWS.close();
      this.combinedTickerWS = null;
      logInfo('Closed futures combined ticker WebSocket');
    }

    this.combinedKlineWS.forEach((ws, timeframe) => {
      ws.close();
      logInfo(`Closed futures combined ${timeframe} kline WebSocket`);
    });
    this.combinedKlineWS.clear();

    // Close individual connections
    this.wsConnections.forEach((ws, key) => {
      ws.close();
      logInfo(`Closed futures WebSocket connection: ${key}`);
    });
    this.wsConnections.clear();

    // Close kline connections
    this.klineConnections.forEach((ws, key) => {
      ws.close();
      logInfo(`Closed futures kline WebSocket connection: ${key}`);
    });
    this.klineConnections.clear();

    // Close order book connections
    this.orderBookConnections.forEach((ws, symbol) => {
      ws.close();
      logInfo(`Closed futures order book WebSocket connection: ${symbol}`);
    });
    this.orderBookConnections.clear();

    // Clear subscribers and caches
    this.subscribers.clear();
    this.allTickersSubscribers.clear();
    this.klineSubscribers.clear();
    this.orderBookSubscribers.clear();
    this.allTickersCache.clear();
    this.klineCache.clear();
    this.orderBookCache.clear();

    // Reset connection state
    this.isAllTickersConnected = false;
    this.reconnectionState.clear();

    logInfo('🔌 All futures WebSocket connections closed and caches cleared');
  }
}
