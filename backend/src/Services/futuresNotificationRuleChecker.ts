import { logInfo, logError, logDebug } from '../utils/logger';
import { prismaService } from './prismaService';
import { FuturesCoinListItem } from './futuresCoinListService';
import { Server as SocketIOServer } from 'socket.io';
import { TechnicalIndicatorResults } from './advancedTechnicalAnalysis';
import { getTechnicalAnalysisService } from './serviceManager';

export interface IndicatorRequirement {
  indicator: string;
  requiredStatus: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

export interface FuturesNotificationRule {
  id: string;
  name: string;
  description?: string | null;
  isActive: boolean;
  minConfidence?: number | null;
  minStrength?: number | null;
  requiredTimeframes?: number | null;
  specificTimeframes?: string[] | null;
  requiredSignalType?: string | null;
  indicatorRequirements?: IndicatorRequirement[] | null;
  priority: string;
  enableSound: boolean;
  enableVisual: boolean;
  marketType: string;
  createdAt: Date;
  updatedAt: Date;
}

export class FuturesNotificationRuleChecker {
  private static instance: FuturesNotificationRuleChecker;
  private lastNotificationTimes: Map<string, Date> = new Map(); // key: ruleId-symbol
  private lastNotificationValues: Map<string, any> = new Map(); // key: ruleId-symbol, value: signal values that triggered notification
  private io: SocketIOServer | null = null;
  private futuresService: any;

  private constructor() {
    // Initialize technical analysis service for indicator calculations
    this.futuresService = getTechnicalAnalysisService();
  }

  public static getInstance(): FuturesNotificationRuleChecker {
    if (!FuturesNotificationRuleChecker.instance) {
      FuturesNotificationRuleChecker.instance = new FuturesNotificationRuleChecker();
    }
    return FuturesNotificationRuleChecker.instance;
  }

  public setSocketIO(io: SocketIOServer): void {
    this.io = io;
  }

  public async checkRulesAgainstCoins(coins: FuturesCoinListItem[]): Promise<void> {
    try {
      logDebug(`🔔 Checking futures notification rules against ${coins.length} futures coins`);

      // Get all active futures notification rules
      const prisma = prismaService.getClient();
      const rules = await prisma.notificationRule.findMany({
        where: {
          isActive: true,
          marketType: 'FUTURES'
        }
      });

      if (rules.length === 0) {
        logDebug('🔔 No active futures notification rules found');
        return;
      }

      logDebug(`🔔 Found ${rules.length} active futures notification rules`);

      let totalTriggered = 0;

      // Check each rule against each coin
      for (const rule of rules) {
        for (const coin of coins) {
          const triggered = await this.checkRuleAgainstCoin(rule as FuturesNotificationRule, coin);
          if (triggered) {
            totalTriggered++;
          }
        }
      }

      if (totalTriggered > 0) {
        logInfo(`🔔 Futures notification check completed: ${totalTriggered} notifications triggered`);
      } else {
        logDebug('🔔 Futures notification check completed: No notifications triggered');
      }
    } catch (error) {
      logError('Error checking futures notification rules against coins', error as Error);
    }
  }

  private async checkRuleAgainstCoin(rule: FuturesNotificationRule, coin: FuturesCoinListItem): Promise<boolean> {
    try {
      // Check if rule conditions are met
      const ruleMatches = await this.evaluateRuleConditions(rule, coin);

      if (!ruleMatches.matches) {
        return false;
      }

      // Check cooldown to prevent spam
      const cooldownKey = `${rule.id}-${coin.symbol}`;
      const lastNotificationTime = this.lastNotificationTimes.get(cooldownKey);
      const cooldownPeriod = this.getCooldownPeriod(rule.priority);
      
      if (lastNotificationTime && (Date.now() - lastNotificationTime.getTime()) < cooldownPeriod) {
        logDebug(`🔔 Futures notification cooldown active for rule ${rule.name} and coin ${coin.symbol}`);
        return false;
      }

      // Check if signal values have changed significantly to avoid duplicate notifications
      const lastValues = this.lastNotificationValues.get(cooldownKey);
      if (lastValues && this.areSignalValuesSimilar(lastValues, ruleMatches.signalData)) {
        logDebug(`🔔 Futures signal values haven't changed significantly for rule ${rule.name} and coin ${coin.symbol}`);
        return false;
      }

      // Create and send notification
      await this.createAndSendNotification(rule, coin, ruleMatches);

      // Update tracking
      this.lastNotificationTimes.set(cooldownKey, new Date());
      this.lastNotificationValues.set(cooldownKey, ruleMatches.signalData);

      return true;
    } catch (error) {
      logError(`Error checking futures rule ${rule.name} against coin ${coin.symbol}`, error as Error);
      return false;
    }
  }

  private async evaluateRuleConditions(rule: FuturesNotificationRule, coin: FuturesCoinListItem): Promise<{ matches: boolean; signalData: any; triggeredTimeframes: string[] }> {
    const triggeredTimeframes: string[] = [];
    let bestConfidence = 0;
    let bestStrength = 0;
    let bestSignal = '';
    let bestTimeframe = '';

    // Check each timeframe
    const timeframesToCheck = rule.specificTimeframes || ['5m', '15m', '1h', '4h', '1d'];
    
    for (const timeframe of timeframesToCheck) {
      const tfData = coin.confidence[timeframe];
      if (!tfData) continue;

      // Check confidence threshold
      if (rule.minConfidence && tfData.confidence < rule.minConfidence) {
        continue;
      }

      // Check strength threshold
      if (rule.minStrength && tfData.strength < rule.minStrength) {
        continue;
      }

      // Check signal type
      if (rule.requiredSignalType && tfData.action !== rule.requiredSignalType) {
        continue;
      }

      // Check indicator requirements
      if (rule.indicatorRequirements && rule.indicatorRequirements.length > 0) {
        const indicatorsMet = await this.checkIndicatorRequirements(rule.indicatorRequirements, coin, timeframe);
        if (!indicatorsMet) {
          continue;
        }
      }

      // This timeframe meets all conditions
      triggeredTimeframes.push(timeframe);
      
      // Track best values for notification
      if (tfData.confidence > bestConfidence) {
        bestConfidence = tfData.confidence;
        bestStrength = tfData.strength;
        bestSignal = tfData.action;
        bestTimeframe = timeframe;
      }
    }

    // Check if minimum timeframes requirement is met
    const requiredTimeframes = rule.requiredTimeframes || 1;
    const matches = triggeredTimeframes.length >= requiredTimeframes;

    return {
      matches,
      signalData: {
        confidence: bestConfidence,
        strength: bestStrength,
        signal: bestSignal,
        timeframe: bestTimeframe,
        triggeredTimeframes
      },
      triggeredTimeframes
    };
  }

  private async checkIndicatorRequirements(requirements: IndicatorRequirement[], coin: FuturesCoinListItem, timeframe: string): Promise<boolean> {
    // If no indicator requirements are specified, skip indicator filtering
    if (!requirements || requirements.length === 0) {
      return true;
    }

    try {
      // Calculate technical indicators on-demand for this coin and timeframe
      const indicators = await this.futuresService.calculateIndicators('binance', coin.symbol, timeframe, 100);

      if (!indicators) {
        logInfo(`${coin.symbol} ${timeframe}: Could not calculate technical indicators for indicator requirements check`);
        return false;
      }

      logInfo(`Checking ${requirements.length} indicator requirements for ${coin.symbol} ${timeframe}`, {
        requirements: requirements,
        indicators: {
          rsi: indicators.rsi,
          macd: indicators.macd,
          ema20: indicators.ema20,
          ema50: indicators.ema50,
          adx: indicators.adx,
          bollingerBands: indicators.bollingerBands
        },
        currentPrice: coin.price
      });

      // Check each indicator requirement individually
      for (const requirement of requirements) {
        const actualStatus = this.getIndicatorStatus(requirement.indicator, indicators, coin.price);

        logInfo(`Indicator check: ${requirement.indicator} is ${actualStatus}, required ${requirement.requiredStatus}`);

        if (actualStatus !== requirement.requiredStatus) {
          logInfo(`Indicator requirement not met: ${requirement.indicator} is ${actualStatus}, required ${requirement.requiredStatus}`);
          return false;
        }
      }

      // All indicator requirements are met
      logInfo('All indicator requirements met');
      return true;
    } catch (error) {
      logError(`Error checking indicator requirements for ${coin.symbol} ${timeframe}`, error as Error);
      return false;
    }
  }

  private getIndicatorStatus(indicatorName: string, indicators: any, currentPrice?: number): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    switch (indicatorName) {
      case 'RSI':
        if (indicators.rsi < 30) return 'BULLISH';
        if (indicators.rsi > 70) return 'BEARISH';
        return 'NEUTRAL';

      case 'MACD':
        if (indicators.macd.MACD > indicators.macd.signal && indicators.macd.histogram > 0) return 'BULLISH';
        if (indicators.macd.MACD < indicators.macd.signal && indicators.macd.histogram < 0) return 'BEARISH';
        return 'NEUTRAL';

      case 'EMA_TREND':
        if (indicators.ema20 > indicators.ema50) return 'BULLISH';
        if (indicators.ema20 < indicators.ema50) return 'BEARISH';
        return 'NEUTRAL';

      case 'BOLLINGER_BANDS':
        if (!currentPrice) return 'NEUTRAL';
        if (currentPrice > indicators.bollingerBands.upper) return 'BEARISH';
        if (currentPrice < indicators.bollingerBands.lower) return 'BULLISH';
        return 'NEUTRAL';

      case 'ADX':
        // ADX measures trend strength, not direction
        // We use EMA trend direction with ADX strength
        if (indicators.adx > 50) {
          if (indicators.ema20 > indicators.ema50) return 'BULLISH';
          if (indicators.ema20 < indicators.ema50) return 'BEARISH';
        }
        return 'NEUTRAL';

      default:
        return 'NEUTRAL';
    }
  }

  private getCooldownPeriod(priority: string): number {
    switch (priority) {
      case 'HIGH': return 5 * 60 * 1000; // 5 minutes
      case 'MEDIUM': return 10 * 60 * 1000; // 10 minutes
      case 'LOW': return 15 * 60 * 1000; // 15 minutes
      default: return 10 * 60 * 1000; // 10 minutes default
    }
  }

  private areSignalValuesSimilar(lastValues: any, currentValues: any): boolean {
    if (!lastValues || !currentValues) return false;
    
    const confidenceDiff = Math.abs(lastValues.confidence - currentValues.confidence);
    const strengthDiff = Math.abs(lastValues.strength - currentValues.strength);
    
    // Consider similar if confidence and strength haven't changed by more than 5%
    return confidenceDiff < 5 && strengthDiff < 5 && lastValues.signal === currentValues.signal;
  }

  private async createAndSendNotification(rule: FuturesNotificationRule, coin: FuturesCoinListItem, ruleMatches: any): Promise<void> {
    try {
      const { signalData, triggeredTimeframes } = ruleMatches;
      
      // Create notification in database
      const prisma = prismaService.getClient();
      const notification = await prisma.notification.create({
        data: {
          title: `FUTURES: ${coin.symbol} ${signalData.signal} Signal`,
          message: `${rule.name}: ${coin.name} shows ${signalData.signal} signal with ${signalData.confidence.toFixed(1)}% confidence`,
          type: 'STRONG_SIGNAL',
          priority: rule.priority,
          symbol: coin.symbol,
          signal: signalData.signal,
          confidence: signalData.confidence,
          strength: signalData.strength,
          timeframe: signalData.timeframe,
          triggeredTimeframes: triggeredTimeframes,
          currentPrice: coin.price,
          exchange: 'Binance Futures',
          hasVisual: rule.enableVisual,
          ruleId: rule.id,
          marketType: 'FUTURES'
        }
      });

      logInfo(`🔔 Created futures notification: ${notification.title} (ID: ${notification.id})`);

      // Send real-time notification via WebSocket
      logInfo(`🔔 Checking WebSocket emission for ${coin.symbol}: io=${!!this.io}, enableVisual=${rule.enableVisual}`);

      if (this.io && rule.enableVisual) {
        const notificationPayload = {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          symbol: notification.symbol,
          signal: notification.signal,
          confidence: notification.confidence,
          strength: notification.strength,
          timeframe: notification.timeframe,
          ruleId: notification.ruleId,
          ruleName: rule.name,
          marketType: 'FUTURES',
          hasVisual: notification.hasVisual,
          createdAt: notification.createdAt.toISOString()
        };

        this.io.emit('notification', notificationPayload);
        logInfo(`🔔 Sent futures WebSocket notification for ${coin.symbol}: ${notification.title}`);
      } else {
        logInfo(`🔔 Skipping WebSocket emission for ${coin.symbol}: io=${!!this.io}, enableVisual=${rule.enableVisual}`);
      }

      // Play sound notification (handled by frontend)
      if (this.io && rule.enableSound) {
        this.io.emit('playFuturesNotificationSound', {
          priority: rule.priority,
          symbol: coin.symbol
        });
      }

    } catch (error) {
      logError('Error creating and sending futures notification', error as Error);
    }
  }
}

// Export singleton instance
export const futuresNotificationRuleChecker = FuturesNotificationRuleChecker.getInstance();
