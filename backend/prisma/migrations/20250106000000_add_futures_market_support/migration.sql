-- Add market type support to existing tables

-- Add marketType column to signal_history table
ALTER TABLE "signal_history" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Add marketType column to notifications table  
ALTER TABLE "notifications" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Add marketType column to notification_rules table
ALTER TABLE "notification_rules" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Create futures-specific early warning alerts table
CREATE TABLE "futures_early_warning_alerts" (
    "id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "exchange" TEXT NOT NULL DEFAULT 'binance',
    "alertType" TEXT NOT NULL,
    "confidence" DOUBLE PRECISION NOT NULL,
    "timeEstimateMin" INTEGER,
    "timeEstimateMax" INTEGER,
    "currentPrice" DOUBLE PRECISION NOT NULL,
    "volume24h" DOUBLE PRECISION,
    "priceChange24h" DOUBLE PRECISION,
    
    -- Phase 1: Volume & Momentum Detection (25 points max)
    "volumeSpike" DOUBLE PRECISION,
    "rsiMomentum" DOUBLE PRECISION,
    "emaConvergence" DOUBLE PRECISION,
    "phase1Score" DOUBLE PRECISION NOT NULL DEFAULT 0,
    
    -- Phase 2: Order Flow Analysis (35 points max)
    "bidAskImbalance" DOUBLE PRECISION,
    "priceAction" DOUBLE PRECISION,
    "phase2Score" DOUBLE PRECISION NOT NULL DEFAULT 0,
    
    -- Phase 3: Whale Activity (40 points max)
    "whaleActivity" DOUBLE PRECISION,
    "phase3Score" DOUBLE PRECISION NOT NULL DEFAULT 0,
    
    "triggeredBy" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isResolved" BOOLEAN NOT NULL DEFAULT false,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "futures_early_warning_alerts_pkey" PRIMARY KEY ("id")
);

-- Create futures alert rules table (separate from spot rules for better organization)
CREATE TABLE "futures_alert_rules" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "minConfidence" DOUBLE PRECISION NOT NULL DEFAULT 50,
    "alertTypes" JSONB NOT NULL DEFAULT '["PUMP_LIKELY", "DUMP_LIKELY"]',
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "futures_alert_rules_pkey" PRIMARY KEY ("id")
);

-- Add indexes for better performance

-- Signal history indexes with market type
CREATE INDEX "signal_history_marketType_symbol_timeframe_idx" ON "signal_history"("marketType", "symbol", "timeframe", "generatedAt");
CREATE INDEX "signal_history_marketType_signal_confidence_idx" ON "signal_history"("marketType", "signal", "confidence", "generatedAt");

-- Notifications indexes with market type
CREATE INDEX "notifications_marketType_createdAt_isRead_idx" ON "notifications"("marketType", "createdAt", "isRead");
CREATE INDEX "notifications_marketType_symbol_signal_idx" ON "notifications"("marketType", "symbol", "signal", "createdAt");

-- Notification rules indexes with market type
CREATE INDEX "notification_rules_marketType_isActive_idx" ON "notification_rules"("marketType", "isActive");

-- Futures early warning alerts indexes
CREATE INDEX "futures_early_warning_alerts_symbol_alertType_idx" ON "futures_early_warning_alerts"("symbol", "alertType", "createdAt");
CREATE INDEX "futures_early_warning_alerts_confidence_createdAt_idx" ON "futures_early_warning_alerts"("confidence", "createdAt");
CREATE INDEX "futures_early_warning_alerts_isActive_isRead_idx" ON "futures_early_warning_alerts"("isActive", "isRead", "createdAt");

-- Futures alert rules indexes
CREATE INDEX "futures_alert_rules_isActive_idx" ON "futures_alert_rules"("isActive");

-- Add check constraints for market type values
ALTER TABLE "signal_history" ADD CONSTRAINT "signal_history_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));
ALTER TABLE "notification_rules" ADD CONSTRAINT "notification_rules_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));

-- Add check constraints for futures alert types
ALTER TABLE "futures_early_warning_alerts" ADD CONSTRAINT "futures_early_warning_alerts_alertType_check" CHECK ("alertType" IN ('PUMP_LIKELY', 'DUMP_LIKELY', 'NEUTRAL'));

-- Add check constraints for priority values
ALTER TABLE "futures_alert_rules" ADD CONSTRAINT "futures_alert_rules_priority_check" CHECK ("priority" IN ('LOW', 'MEDIUM', 'HIGH'));

-- Insert default futures alert rule
INSERT INTO "futures_alert_rules" ("id", "name", "description", "minConfidence", "alertTypes", "priority") 
VALUES (
    'default-futures-rule',
    'Default Futures Alert Rule',
    'Default rule for futures early warning alerts with 60% minimum confidence',
    60.0,
    '["PUMP_LIKELY", "DUMP_LIKELY"]',
    'MEDIUM'
);

-- Update existing data to have SPOT market type (already set as default, but ensuring consistency)
UPDATE "signal_history" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
UPDATE "notifications" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
UPDATE "notification_rules" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
